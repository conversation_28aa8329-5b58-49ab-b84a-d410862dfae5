<template>
    <el-tabs v-model="activeTab" class="full-tabs">
        <el-tab-pane name="products" :label="'Products' | t">
            <tab-products />
        </el-tab-pane>

        <el-tab-pane name="prices" :label="'Prices' | t">
            <tab-prices />
        </el-tab-pane>

        <el-tab-pane name="payments" :label="'Payments' | t">
            <tab-payments />
        </el-tab-pane>

        <el-tab-pane name="installments" :label="'Installments' | t">
            <tab-installments />
        </el-tab-pane>

        <el-tab-pane name="customers" :label="'Customers' | t">
            <tab-customers />
        </el-tab-pane>

        <el-tab-pane name="b2b" :label="'B2B Policies' | t">
            <tab-b2b />
        </el-tab-pane>
    </el-tabs>
</template>

<script>
import TabProducts from './tab-policies/_tab-products';
import TabPrices from './tab-policies/_tab-prices';
import TabInstallments from './tab-policies/_tab-installments';
import TabPayments from './tab-policies/_tab-payments';
import TabCustomers from './tab-policies/_tab-customers';
import TabB2b from './tab-policies/_tab-b2b';

export default {
    props: {
        model: Object
    },

    data: () => ({
        activeTab: 'products'
    }),

    components: {
        TabProducts,
        TabPrices,
        TabInstallments,
        TabPayments,
        TabCustomers,
        TabB2b
    }
};
</script>
