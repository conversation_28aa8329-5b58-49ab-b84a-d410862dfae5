<template>
    <el-scrollbar>
        <div class="columns">
            <div class="column">
                <ui-legend title="General" />
                <ui-field name="code" />
                <ui-field name="name" />

                <ui-legend title="Pricing" class="mt30" />
                <ui-field name="pricingPolicy" :options="pricingPolicyOptions" translate-labels />
                <ui-field
                    name="taxApplication"
                    :options="taxApplicationOptions"
                    translate-labels
                    :disabled="!!integrationType && integrationType.name !== 'enterstore'"
                />
                <ui-field
                    name="priceListId"
                    collection="sale.price-lists"
                    :extra-fields="['code']"
                    :template="'{{code}} - {{name}}'"
                    :filters="{module: 'ecommerce'}"
                    v-show="$setting('sale.salesPriceList') && model.pricingPolicy === 'use-price-list'"
                />
                <ui-field
                    name="discountedPriceListId"
                    collection="sale.price-lists"
                    :extra-fields="['code']"
                    :template="'{{code}} - {{name}}'"
                    :filters="{module: 'ecommerce'}"
                    v-show="$setting('sale.salesPriceList') && model.pricingPolicy === 'use-price-list'"
                />
                <ui-field name="useProductImportPrices" />
                <ui-field name="usePosBankMatching" v-show="model.integrationType === 'enterstore'" />
                <ui-field name="applyZeroTax" v-show="model.integrationType === 'enterstore'" />
                <ui-field name="useFixedExchangeRates" />
                <ui-field
                    name="useSellout"
                    v-show="$setting('sale.sellout') && model.integrationType === 'enterstore'"
                />

                <ui-legend title="Stock" class="mt30" />
                <ui-field name="stockPolicy" :options="stockPolicyOptions" translate-labels />
                <ui-field name="useProductImportStocks" />
                <ui-field name="publishOutOfStockItems" />
                <ui-field name="useAlternateProductsWhenStockRunsOut" v-show="model.integrationType === 'enterstore'" />

                <ui-legend title="Integration" class="mt30" />
                <ui-field name="integrationType" :options="integrationTypeOptions" />
                <ui-field name="integrationParams" :rows="6" v-show="hasParams" />
            </div>

            <div class="column">
                <ui-legend title="Details" />
                <ui-field name="languageId" collection="kernel.languages" />
                <ui-field name="currencyId" collection="kernel.currencies" v-show="$setting('system.multiCurrency')" />
                <ui-field
                    name="customerGroupId"
                    collection="kernel.partner-groups"
                    view="system.management.configuration.partner-groups"
                    :filters="{type: 'customer'}"
                    disable-detail
                    disable-create
                />
                <ui-field
                    name="branchId"
                    collection="kernel.branches"
                    view="system.management.configuration.branches"
                    :extra-fields="['code']"
                    :template="'{{code}} - {{name}}'"
                    disable-detail
                    disable-create
                    v-show="$setting('system.multiBranch')"
                />
                <ui-field
                    name="warehouseId"
                    collection="inventory.warehouses"
                    view="inventory.configuration.warehouses"
                    :filters="{branchId: model.branchId}"
                    :extra-fields="['shortName']"
                    :template="'{{shortName}} - {{name}}'"
                    disable-detail
                    disable-create
                    :disabled="!model.branchId"
                />
                <ui-field
                    name="returnWarehouseId"
                    collection="inventory.warehouses"
                    view="inventory.configuration.warehouses"
                    :filters="{branchId: model.branchId}"
                    :extra-fields="['shortName']"
                    :template="'{{shortName}} - {{name}}'"
                    disable-detail
                    disable-create
                    :disabled="!model.branchId"
                    v-show="model.integrationType === 'enterstore'"
                />
                <ui-field name="salesDocumentTypeId" collection="sale.document-types" />
                <ui-field
                    name="sourceId"
                    collection="sale.sources"
                    view="sale.configuration.sources"
                    disable-create
                    disable-detail
                />
                <ui-field
                    name="communicationChannelId"
                    collection="kernel.communication-channels"
                    view="system.management.configuration.communication-channels"
                    disable-create
                    disable-detail
                />
                <ui-field name="website" />
                <ui-field name="email" />
                <ui-field name="tagManagerId" v-show="model.integrationType === 'enterstore'">
                    <div slot="append">{{ 'GTM-XXXXXXX' }}</div>
                </ui-field>
                <ui-field
                    name="pcmModelId"
                    collection="pcm.models"
                    view="pcm.product-models.models"
                    :filters="{configuratorType: 'product-finder'}"
                    v-show="$app.hasModule('pcm') && model.integrationType === 'enterstore'"
                    disable-create
                    disable-detail
                />
                <ui-field
                    name="allowedPartnerGroupIds"
                    collection="kernel.partner-groups"
                    :filters="{type: 'customer'}"
                    v-show="model.useB2BPolicies && model.integrationType === 'enterstore'"
                />
                <ui-field
                    name="visibleStockWarehouseIds"
                    collection="inventory.warehouses"
                    v-show="model.useB2BPolicies && model.integrationType === 'enterstore'"
                />
                <ui-field name="evaluateVariations" v-show="model.integrationType === 'enterstore'" />
                <ui-field name="automaticOrderApproval" v-show="model.integrationType !== 'enterstore'" />
                <ui-field name="createDraftOrder" v-show="model.integrationType === 'enterstore'" />
                <ui-field
                    name="updateMarketplaceOrderStatus"
                    v-show="model.integrationType !== 'enterstore' && model.automaticOrderApproval"
                />
                <ui-field
                    name="automaticDelivery"
                    v-show="model.integrationType !== 'enterstore' && model.automaticOrderApproval"
                />
                <ui-field
                    name="automaticDeliveryConfirmation"
                    v-show="model.integrationType !== 'enterstore' && model.automaticDelivery"
                />
                <ui-field
                    name="automaticShippingOrder"
                    v-show="model.integrationType !== 'enterstore' && model.automaticDeliveryConfirmation"
                />
                <ui-field name="automaticInvoice" v-show="model.automaticOrderApproval" />
                <ui-field
                    name="automaticInvoiceApproval"
                    v-show="model.integrationType !== 'enterstore' && model.automaticInvoice"
                />
                <ui-field
                    name="automaticInvoiceSending"
                    v-show="model.integrationType !== 'enterstore' && model.automaticInvoiceApproval"
                />
                <ui-field name="barcodeMatchingOnly" v-show="model.integrationType !== 'enterstore'" />
                <ui-field name="automaticOrderCancellation" v-show="model.integrationType === 'enterstore'" />
                <ui-field name="disableReturnCreation" v-show="model.integrationType === 'enterstore'" />
                <ui-field name="useB2BPolicies" v-show="model.integrationType === 'enterstore'" />
                <ui-field name="sendB2bMail" v-show="model.integrationType === 'enterstore' && model.useB2BPolicies" />
                <ui-field name="isActive" />

                <ui-legend title="Delivery Settings" class="mt30" />
                <ui-field name="defaultDeliveryOptionId" :options="defaultDeliveryOptionOptions" />
                <ui-field name="defaultVolumetricWeight" v-show="model.integrationType === 'enterstore'" />
                <ui-field name="calculateDeliveryPricePerItem" v-show="model.integrationType === 'enterstore'" />
                <ui-field name="showEstimatedDeliveryDuration" v-show="model.integrationType === 'enterstore'" />
                <ui-field
                    name="estimatedDeliveryDuration"
                    v-show="
                        (model.integrationType === 'enterstore' && !!model.showEstimatedDeliveryDuration) ||
                        model.integrationType !== 'enterstore'
                    "
                >
                    <div slot="append">{{ 'Day(s)' | t }}</div>
                </ui-field>
                <ui-field name="defaultReturnOrderDuration" v-show="model.integrationType === 'enterstore'">
                    <div slot="append">{{ 'Day(s)' | t }}</div>
                </ui-field>
                <ui-field
                    name="storageServiceProductId"
                    collection="inventory.products"
                    view="inventory.catalog.products"
                    :filters="{type: 'service', isSimple: true}"
                    :extra-fields="['code', 'definition']"
                    :template="'{{code}} - {{definition}}'"
                    disable-create
                    disable-detail
                    v-show="model.integrationType === 'enterstore'"
                />
                <ui-field
                    name="storageTaxId"
                    collection="kernel.taxes"
                    :filters="{scope: 'sale'}"
                    disable-create
                    disable-detail
                    v-show="model.integrationType === 'enterstore'"
                />

                <ui-legend title="Export Invoice Settings" class="mt30" />
                <ui-field name="exportInvoiceScenario" :options="exportInvoiceScenarioOptions" translate-labels />
                <ui-field
                    name="exportEInvoiceTypeId"
                    :options="exportEInvoiceTypeIdOptions"
                    v-show="model.exportInvoiceScenario"
                />
                <ui-field
                    name="exportEInvoiceTypeConditionCode"
                    :options="exportEInvoiceTypeConditionOptions"
                    v-show="exportEInvoiceTypeConditionOptions.length > 0"
                />

                <ui-legend
                    title="Invoice Settings"
                    class="mt30"
                    v-show="isEInvoiceEnabled || isEArchiveInvoiceEnabled"
                />
                <ui-field
                    name="eInvoiceNumberingId"
                    collection="kernel.numbering"
                    view="system.management.configuration.numbering"
                    :filters="eInvoiceNumberingIdFilters"
                    v-show="isEInvoiceEnabled"
                />
                <ui-field
                    name="eInvoiceTemplateId"
                    collection="eops.templates"
                    label-from="title"
                    :filters="{
                        type: {
                            $in: ['commercial-invoice']
                        },
                        $and: [{title: {$exists: true}}, {title: {$ne: null}}, {title: {$ne: ''}}]
                    }"
                    v-show="isEInvoiceEnabled"
                />
                <ui-field
                    name="eArchiveInvoiceNumberingId"
                    collection="kernel.numbering"
                    view="system.management.configuration.numbering"
                    :filters="eArchiveInvoiceNumberingIdFilters"
                    v-show="isEArchiveInvoiceEnabled"
                />
                <ui-field
                    name="eArchiveInvoiceTemplateId"
                    collection="eops.templates"
                    label-from="title"
                    :filters="{
                        type: {
                            $in: ['e-archive-invoice']
                        },
                        $and: [{title: {$exists: true}}, {title: {$ne: null}}, {title: {$ne: ''}}]
                    }"
                    v-show="isEArchiveInvoiceEnabled"
                />
            </div>
        </div>
    </el-scrollbar>
</template>

<script>
import _ from 'lodash';

export default {
    props: {
        model: Object,
        integrationTypes: Array,
        integrationType: Object,
        eInvoiceTypes: Array,
        exportEInvoiceTypeConditionOptions: Array,
        eArchiveInvoiceNumberingIds: Array,
        eInvoiceNumberingIds: Array
    },

    data: () => ({
        taxApplicationOptions: [
            {value: 'tax-included', label: 'Tax included'},
            {value: 'tax-excluded', label: 'Tax excluded'}
        ],
        stockPolicyOptions: [
            {value: 'manuel', label: 'Manuel'},
            {value: 'use-stock-on-hand', label: 'Use stock on hand'},
            {value: 'use-available-stock', label: 'Use available stock'},
            {
                value: 'use-available-stock-without-ordered-reservations',
                label: 'Use available stock (Ordered stocks excluded)'
            }
        ]
    }),

    computed: {
        eArchiveInvoiceNumberingIdFilters() {
            return {
                _id: {
                    $in: [
                        ...this.$setting('eops.eArchiveInvoiceNumberings'),
                        ...(Array.isArray(this.eArchiveInvoiceNumberingIds) ? this.eArchiveInvoiceNumberingIds : [])
                    ]
                },
                $disableInUseCheck: true
            };
        },
        eInvoiceNumberingIdFilters() {
            return {
                _id: {
                    $in: [
                        ...this.$setting('eops.eInvoiceNumberings'),
                        ...(Array.isArray(this.eInvoiceNumberingIds) ? this.eInvoiceNumberingIds : [])
                    ]
                },
                $disableInUseCheck: true
            };
        },
        exportInvoiceScenarioOptions() {
            return [{value: 'e-archive-invoice', label: 'E-archive invoice'}];
        },
        exportEInvoiceTypeIdOptions() {
            return this.eInvoiceTypes
                .filter(type => type.scenarios.indexOf(this.model.exportInvoiceScenario) !== -1)
                .map(type => ({
                    value: type._id,
                    label: type.name
                }));
        },
        pricingPolicyOptions() {
            if (!this.$setting('sale.salesPriceList')) {
                return [
                    {value: 'manuel', label: 'Manuel'},
                    {value: 'use-product-sales-price', label: 'Use product sales price'}
                ];
            }

            return [
                {value: 'manuel', label: 'Manuel'},
                {value: 'use-product-sales-price', label: 'Use product sales price'},
                {value: 'use-price-list', label: 'Use price list'}
            ];
        },
        integrationTypeOptions() {
            return this.integrationTypes.map(i => ({
                value: i.name,
                label: i.title
            }));
        },
        hasParams() {
            const model = this.model;
            const integrationType = this.integrationTypes.find(i => i.name === model.integrationType);

            return !!integrationType && _.isPlainObject(integrationType.params);
        },
        defaultDeliveryOptionOptions() {
            return (this.model.deliveryOptions || []).map(option => ({
                value: option.id,
                label: option.name
            }));
        },
        isEInvoiceEnabled() {
            return !!this.$setting('eops.isEInvoiceActivated');
        },
        isEArchiveInvoiceEnabled() {
            return !!this.$setting('eops.isEArchiveInvoiceActivated');
        }
    }
};
</script>
