<template>
    <ui-view
        type="form"
        :schema="schema"
        :model="model"
        :before-init="beforeInit"
        :before-submit="beforeSubmit"
        :before-validate="beforeValidate"
        :dialog-width="480"
        :dialog-height="300"
    >
        <ui-legend :title="'B2B Mail Settings' | t" />
        <ui-field name="sendB2bMail" />
        
        <ui-legend :title="'Partner' | t" class="mt30" />
        <ui-field name="partnerType" :options="partnerTypeOptions" translate-labels />
        <kernel-common-partner-select
            name="partnerId"
            :filters="{type: 'customer'}"
            disable-create
            v-show="model.partnerType === 'single-partner'"
        />
        <ui-field
            name="partnerGroupIds"
            collection="kernel.partner-groups"
            view="system.management.configuration.partner-groups"
            :filters="{type: 'customer'}"
            disable-create
            v-show="model.partnerType === 'partner-group'"
        />
    </ui-view>
</template>

<script>
export default {
    data: () => ({
        model: {},
        partnerTypeOptions: [
            {value: 'single-partner', label: 'Single customer'},
            {value: 'partner-group', label: 'Customer group'}
        ]
    }),

    computed: {
        schema() {
            return {
                sendB2bMail: {
                    type: 'boolean',
                    label: 'Send B2B mail',
                    default: false
                },
                partnerType: {
                    type: 'string',
                    label: 'Customer type'
                },
                partnerId: {
                    type: 'string',
                    label: 'Customer',
                    required: false
                },
                partnerGroupIds: {
                    type: ['string'],
                    label: 'Customer group',
                    required: false
                }
            };
        }
    },

    methods: {
        beforeInit(model) {
            if (!!this.$params('model')) {
                model = this.$params('model');
            }
            if (!model.partnerType) {
                model.partnerType = 'single-partner';
            }

            return model;
        },
        beforeValidate(model) {
            if (model.partnerType === 'single-partner' && !model.partnerId) {
                throw new this.$app.errors.Unprocessable({
                    message: this.$t('{{label}} is required', {label: this.$t('Partner')}),
                    field: 'partnerId'
                });
            }
            if (
                model.partnerType === 'partner-group' &&
                !(Array.isArray(model.partnerGroupIds) && model.partnerGroupIds.length > 0)
            ) {
                throw new this.$app.errors.Unprocessable({
                    message: this.$t('{{label}} is required', {label: this.$t('Partner group')}),
                    field: 'partnerGroupIds'
                });
            }

            return model;
        },
        beforeSubmit(model) {
            if (model.partnerType === 'single-partner') {
                model.partnerGroupIds = [];
            } else if (model.partnerType === 'partner-group') {
                model.partnerId = '';
            }

            return model;
        }
    }
};
</script>
