<template>
    <div class="tab-b2b">
        <div class="tab-b2b-header">
            <ui-button
                type="primary"
                icon="plus"
                @click="handleCreate"
                :disabled="$params('loading')"
            >
                {{ 'Create' | t }}
            </ui-button>

            <ui-button
                type="danger"
                icon="trash"
                @click="handleDelete"
                :disabled="$params('loading') || selected.length === 0"
            >
                {{ 'Delete' | t }}
            </ui-button>
        </div>

        <div class="tab-b2b-content">
            <ui-table
                collection="store.b2b-policies"
                :columns="columns"
                :filters="filters"
                :extra-fields="[]"
                @double-clicked="handleDetail"
                @selected="handleSelect"
            />
        </div>
    </div>
</template>

<script>
import _ from 'lodash';
import fastCopy from 'fast-copy';
import B2bPolicies from './_b2b-policies';

export default {
    data: () => ({
        scopeQuery: {},
        selected: [],
        partnerGroups: []
    }),

    computed: {
        filters() {
            const filters = fastCopy(this.scopeQuery);

            filters.storeId = this.$params('id');

            return filters;
        },
        columns() {
            const self = this;

            return [
                {
                    name: 'sendB2bMail',
                    label: this.$t('Send B2B Mail'),
                    type: 'boolean',
                    width: 120
                },
                {
                    name: 'partnerType',
                    label: this.$t('Customer Type'),
                    width: 150,
                    formatter: value => {
                        if (value === 'single-partner') return this.$t('Single customer');
                        if (value === 'partner-group') return this.$t('Customer group');
                        return value;
                    }
                },
                {
                    name: 'partnerId',
                    label: this.$t('Customer'),
                    width: 200,
                    formatter: (value, row) => {
                        if (row.partnerType === 'single-partner' && value) {
                            return this.$collection('kernel.partners').findOne({_id: value})?.name || value;
                        }
                        return '';
                    }
                },
                {
                    name: 'partnerGroupIds',
                    label: this.$t('Customer Groups'),
                    width: 200,
                    formatter: (value, row) => {
                        if (row.partnerType === 'partner-group' && Array.isArray(value) && value.length > 0) {
                            const groups = self.partnerGroups.filter(group => value.includes(group._id));
                            return groups.map(group => group.name).join(', ');
                        }
                        return '';
                    }
                }
            ];
        }
    },

    methods: {
        handleSelect(selected) {
            this.selected = selected;
        },
        async handleCreate() {
            this.$params('loading', true);

            try {
                this.$program.dialog({
                    component: B2bPolicies,
                    params: {
                        title: this.$t('B2B Policies')
                    },
                    onSubmit: async data => {
                        data.storeId = this.$params('id');
                        await this.$collection('store.b2b-policies').create(data);
                    }
                });
            } catch (error) {
                this.$program.message('error', error.message);
            }

            this.$params('loading', false);
        },
        async handleDelete() {
            this.$params('loading', true);

            try {
                await this.$program.confirm(this.$t('Are you sure you want to delete the selected items?'));

                const ids = this.selected.map(item => item._id);
                await this.$collection('store.b2b-policies').remove({_id: {$in: ids}});

                this.selected = [];
            } catch (error) {
                if (error.message !== 'cancel') {
                    this.$program.message('error', error.message);
                }
            }

            this.$params('loading', false);
        },
        async handleDetail(row) {
            this.$params('loading', true);

            const model = await this.$collection('store.b2b-policies').findOne({
                _id: row._id,
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });

            try {
                this.$program.dialog({
                    component: B2bPolicies,
                    params: {
                        title: this.$t('B2B Policies'),
                        model
                    },
                    onSubmit: async data => {
                        await this.$collection('store.b2b-policies').patch({_id: row._id}, data);
                    }
                });
            } catch (error) {
                this.$program.message('error', error.message);
            }

            this.$params('loading', false);
        }
    },

    async created() {
        this.$params('loading', true);

        this.partnerGroups = await this.$collection('kernel.partner-groups').find({
            type: 'customer',
            $select: ['_id', 'name']
        });

        this.$params('loading', false);
    }
};
</script>

<style lang="scss">
.tab-b2b {
    .tab-b2b-header {
        display: flex;
        gap: 8px;
        margin-bottom: 16px;
    }
}
</style>
