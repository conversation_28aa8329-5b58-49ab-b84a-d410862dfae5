export default {
    name: 'b2b-policies',
    softDelete: false,
    schema: {
        storeId: {
            type: 'string',
            label: 'Store',
            index: true
        },
        sendB2bMail: {
            type: 'boolean',
            label: 'Send B2B mail',
            default: false
        },
        partnerType: {
            type: 'string',
            label: 'Partner type'
        },
        partnerId: {
            type: 'string',
            label: 'Partner',
            required: false,
            index: true
        },
        partnerGroupIds: {
            type: ['string'],
            label: 'Partner group',
            required: false,
            index: true
        }
    },
    attributes: {
        store: {
            collection: 'ecommerce.stores',
            parentField: 'storeId',
            childField: '_id'
        },
        partner: {
            collection: 'kernel.partners',
            parentField: 'partnerId',
            childField: '_id'
        }
    }
};
