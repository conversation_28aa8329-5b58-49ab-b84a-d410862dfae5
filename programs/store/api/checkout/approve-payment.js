import _ from 'lodash';
import fastCopy from 'fast-copy';
import hasher from 'object-hash';
import fs from 'fs-extra';
import path from 'path';
import Random from 'framework/random';
import {template, trim} from 'framework/helpers';
import {calculateReport} from '../../../finance/components/pp/utils';
import {normalizeCart} from '../cart/utils';
import microtime from 'microtime';
import {calculateSelloutAmount, getPaymentPolicy, getPolicyPrices, getPricePolicy} from '../utils';

export default async function (app, store, request, response) {
    const {cartId, payload} = request.body;

    // Get cart.
    let cart = await app.collection('store.cart').findOne({_id: cartId, status: 'draft'});
    if (!cart) {
        return response.status(404).json({
            status: 'error',
            code: 404,
            message: 'Not found'
        });
    }
    await app.collection('store.cart').patch(
        {_id: cart._id},
        {
            isApproving: true
        }
    );
    cart = await normalizeCart(app, store, cart);
    cart.items = cart.items.filter(item => item.productStockQuantity > 0);
    if (cart.items.length < 1) {
        return response.status(400).json({
            status: 'error',
            code: 400,
            message: 'Invalid cart!'
        });
    }

    try {
        const user = await app.collection('kernel.users').findOne({isRoot: true});
        const now = app.datetime.local();

        // Warehouse for store-delivery
        let storeDeliveryWarehouse = null;
        if (cart.deliveryType === 'store-delivery') {
            if (!cart.storeDeliveryWarehouseId) throw new Error('No store is found for delivery!');

            storeDeliveryWarehouse = await app.collection('inventory.warehouses').findOne({
                _id: cart.storeDeliveryWarehouseId,
                $select: ['_id', 'branchId', 'address'],
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });
        }

        // Get company.
        const company = await app.collection('kernel.company').findOne({});

        // Get customer.
        const {
            customer,
            contact,
            invoiceResponsibleId,
            invoiceAddressId,
            invoiceAddress,
            deliveryReceiverId,
            deliveryAddressId,
            deliveryAddress,
            isNewCustomer
        } = await getCustomer({app, company, store, cart, payload, now, user});

        // Get order.
        const order = await getOrder({
            app,
            company,
            store,
            cart,
            payload,
            now,
            user,
            customer,
            contact,
            invoiceResponsibleId,
            invoiceAddressId,
            invoiceAddress,
            deliveryReceiverId,
            deliveryAddressId,
            deliveryAddress,
            storeDeliveryWarehouse
        });

        // Update cart as completed.
        const newCart = await app.collection('store.cart').patch(
            {
                _id: cart._id
            },
            {
                status: 'completed',
                customerId: customer._id,
                orderId: order._id,
                ...(_.isPlainObject(storeDeliveryWarehouse) && {
                    deliveryAddress: storeDeliveryWarehouse.address,
                    deliveryAddressId: ''
                }),
                isApproving: false
            }
        );

        // noinspection ES6MissingAwait
        (async () => {
            try {
                // Update products sales count.
                await updateProductsSalesCount(app, store, cart);

                // Send order details and new customer email to customer.
                await sendSalesMail(app, company, store, newCart, order);

                // Check if this is a B2B order and send company mail if enabled
                await checkAndSendB2bMail(app, company, store, newCart, order, customer);
            } catch (error) {
                console.error('Approve payment', error.message);
            }
        })();

        // Send result.
        return response.json({
            cart: newCart,
            orderId: order._id,
            orderCode: order.code,
            isNewCustomer
        });
    } catch (error) {
        console.error('Failed to create ecommerce order:', error);

        try {
            await fs.writeFile(path.join(process.cwd(), '../', 'approve-order-errors.txt'), error.message);
        } catch (e) {}

        return response.status(400).json({
            status: 'error',
            code: error.code || 400,
            message: error.message || 'Unknown error'
        });
    }
}

export async function sendSalesMail(app, company, store, cart, order) {
    try {
        const t = text => app.translate(text);
        const siteUrl = trim(store.website, '/');
        const deliveryAddress = await normalizeAddress(app, cart.deliveryAddress);
        const billingAddress = await normalizeAddress(app, cart.billingAddress);

        let paymentMethod = null;
        if (!!cart.subPaymentMethodId) {
            paymentMethod = store.paymentMethods.find(
                paymentMethod =>
                    paymentMethod.id === cart.subPaymentMethodId && paymentMethod.paymentType === 'money-order'
            );
        }

        let bankAccount = null;
        if (paymentMethod && paymentMethod.journalId) {
            bankAccount = await app.collection('accounting.bank-accounts').findOne({
                journalId: paymentMethod.journalId,
                $select: ['name', 'iban'],
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });
        }

        const estimatedDeliveryDuration =
            typeof cart.estimatedDeliveryDuration === 'number' ? cart.estimatedDeliveryDuration : 0;

        const deliveryDate =
            estimatedDeliveryDuration > 0
                ? app.format(app.datetime.local().plus({days: estimatedDeliveryDuration}).toJSDate(), 'date')
                : '---';

        const isStoreDeliveryOrder = cart.deliveryType === 'store-delivery';

        const template = `
<mj-wrapper full-width
            background-color="#dcfce7"
            padding="24px"
            css-class="content-wrapper">
    <mj-section padding-bottom="48px" padding-top="32px">
        <mj-column>
            <mj-image align="center" src="${app.absoluteUrl('static/images/mail/order-received.png')}" width="150px"/>
        </mj-column>
    </mj-section>

    <mj-section>
        <mj-column>
            <mj-text font-size="21px"
                     color="#16a34a"
                     font-weight="600" padding="0" padding-bottom="12px">${t('Your Order Received')}</mj-text>
            <mj-text>${
                isStoreDeliveryOrder
                    ? t('You will receive an email notification when your order is ready for pickup.')
                    : t('We will notify you by e-mail when your order is shipped.')
            }</mj-text>
        </mj-column>
    </mj-section>

    <mj-section padding-top="16px">
        <mj-column padding-bottom="12px">
            <mj-text font-size="13px" font-weight="600" padding="0">${t('ORDER NUMBER')}</mj-text>
            <mj-text font-size="13px" padding="0">
                <a href="${siteUrl}/account/my-orders/${order._id}">${order.code}</a>
            </mj-text>
        </mj-column>

        <mj-column>
            <mj-text font-size="13px" font-weight="600" padding="0">${t('TOTAL AMOUNT')}</mj-text>
            <mj-text font-size="13px" padding="0">${app.format(cart.grandTotal || 0, 'currency')}</mj-text>
        </mj-column>
    </mj-section>

    <mj-section padding-top="12px">
        <mj-column>
            <mj-text font-size="13px" font-weight="600" padding="0">${t('DELIVERY ADDRESS')}</mj-text>
            <mj-text font-size="13px" padding="0">${deliveryAddress.address}</mj-text>
        </mj-column>
    </mj-section>
</mj-wrapper>

<mj-section padding-top="48px" padding-bottom="16px">
    <mj-column>
        <mj-text padding="0" font-size="18px" font-weight="600">${t('Order Summary')}</mj-text>
    </mj-column>
</mj-section>
<mj-wrapper background-color="white"
            padding-left="24px"
            padding-right="24px"
            padding-bottom="12px"
            padding-top="24px"
            css-class="content-wrapper-with-border">
${cart.items
    .map(item => {
        return `
<mj-section>
    <mj-column width="15%">
        <mj-image padding="0" padding-right="32px" padding-bottom="12px" src="${item.productImage}.png"/>
    </mj-column>

    <mj-column width="85%">
        <mj-text font-size="15px" padding="0">
            <a href="${!!item.productLink ? `${siteUrl}${item.productLink}` : `${siteUrl}/${item.productSlug}`}">
                ${item.productName}
            </a>
        </mj-text>
        <mj-text padding="0" padding-top="8px" font-size="14px" font-weight="500">
            ${item.quantity} x ${app.format(
            (typeof item.discountedPrice === 'number' && item.discountedPrice > 0
                ? item.discountedPrice
                : item.price) || 0,
            'currency'
        )}
        </mj-text>
    </mj-column>
</mj-section>

<mj-section>
    <mj-column>
        <mj-divider padding-top="16px" padding-bottom="16px"/>
    </mj-column>
</mj-section>
`;
    })
    .join('\n')}

    <mj-section>
       <mj-group>
           <mj-column>
               <mj-text font-size="14px" padding="0" line-height="21px">
                   ${t('Products Total')} (${cart.productCount})
               </mj-text>
           </mj-column>

           <mj-column>
               <mj-text align="right" font-size="14px" padding="0" line-height="21px">
                   ${app.format(cart.subTotal || 0, 'currency')}
               </mj-text>
           </mj-column>
       </mj-group>
    </mj-section>
    <mj-section padding-top="4px">
        <mj-group>
            <mj-column>
                <mj-text font-size="14px" padding="0" line-height="21px">
                    ${t('Tax total')}
                </mj-text>
            </mj-column>

            <mj-column>
                <mj-text align="right" font-size="14px" padding="0" line-height="21px">
                      ${app.format(cart.taxTotal || 0, 'currency')}
                </mj-text>
            </mj-column>
        </mj-group>
    </mj-section>
    <mj-section padding-top="4px">
        <mj-group>
            <mj-column>
                <mj-text font-size="14px" padding="0" line-height="21px">
                    ${t('Delivery amount')}
                </mj-text>
            </mj-column>

            <mj-column>
                <mj-text align="right" font-size="14px" padding="0" line-height="21px">
                      ${app.format(cart.deliveryTotal || 0, 'currency')}
                </mj-text>
            </mj-column>
        </mj-group>
    </mj-section>
    ${
        (cart.cashOnDeliveryServiceFee || 0) > 0
            ? `
<mj-section padding-top="4px">
    <mj-group>
        <mj-column>
            <mj-text font-size="14px" padding="0" line-height="21px">
                ${t('Cash on delivery service fee')}
            </mj-text>
        </mj-column>

        <mj-column>
            <mj-text align="right" font-size="14px" padding="0" line-height="21px">
                ${app.format(cart.cashOnDeliveryServiceFee || 0, 'currency')}
            </mj-text>
        </mj-column>
    </mj-group>
</mj-section>
    `
            : ``
    }
    ${
        (cart.dueDifference || 0) > 0
            ? `
<mj-section padding-top="4px">
    <mj-group>
        <mj-column>
            <mj-text font-size="14px" padding="0" line-height="21px">
                ${t('Due difference amount')}
            </mj-text>
        </mj-column>

        <mj-column>
            <mj-text align="right" font-size="14px" padding="0" line-height="21px">
                ${app.format(cart.dueDifference || 0, 'currency')}
            </mj-text>
        </mj-column>
    </mj-group>
</mj-section>
    `
            : ``
    }
        ${
            Array.isArray(cart.discounts) && cart.discounts.length > 0
                ? cart.discounts
                      .filter(discount => !discount.isRowDiscount)
                      .map(
                          discount => `
<mj-section padding-top="4px">
    <mj-group>
        <mj-column>
            <mj-text font-size="14px" padding="0" line-height="21px">
                ${discount.description}
            </mj-text>
        </mj-column>

        <mj-column>
            <mj-text align="right" font-size="14px" padding="0" line-height="21px">
                -${app.format(discount.amount || 0, 'currency')}
            </mj-text>
        </mj-column>
    </mj-group>
</mj-section>
    `
                      )
                      .join('\n')
                : ``
        }
    <mj-section padding-top="8px">
       <mj-group>
           <mj-column>
               <mj-text font-size="16px" padding="0" font-weight="600" line-height="21px">
                    ${t('Grand total')}
               </mj-text>
           </mj-column>

           <mj-column>
               <mj-text align="right" font-weight="600" font-size="16px" padding="0" line-height="21px">
                    ${app.format(cart.grandTotal || 0, 'currency')}
               </mj-text>
           </mj-column>
       </mj-group>
    </mj-section>

    <mj-section padding-top="16px">
        <mj-column>
            <mj-divider padding-top="16px" padding-bottom="16px"/>
        </mj-column>
    </mj-section>
    <mj-section padding-top="0">
        ${
            isStoreDeliveryOrder
                ? ''
                : `<mj-column>
                    <mj-text font-size="14px" font-weight="600" padding="0"  padding-bottom="8px">${t(
                        'Estimated Delivery Date'
                    )}</mj-text>
                    <mj-text font-size="13px" padding="0" line-height="21px">
                    ${deliveryDate}
                    </mj-text>
                </mj-column>`
        }

        <mj-column>

        </mj-column>

        <mj-column>
            <mj-text font-size="14px" font-weight="600" padding="0"  padding-bottom="8px">${t(
                'Payment Information'
            )}</mj-text>
            <mj-text font-size="13px" padding="0" line-height="21px">
			    ${company.legalName || company.name}
            </mj-text>
            <mj-text font-size="13px" padding="0" line-height="21px">
               ${
                   bankAccount
                       ? t('Money Transfer')
                       : cart.installmentCount > 1
                       ? app.translate('{{installmentCount}} installments', {installmentCount: cart.installmentCount})
                       : t('Single installment')
               }
            </mj-text>
            <mj-text font-size="13px" padding="0" line-height="21px">
			    ${bankAccount?.name || ''}
            </mj-text>
            <mj-text font-size="13px" padding="0" line-height="21px">
			    ${bankAccount?.iban || ''}
            </mj-text>
        </mj-column>
    </mj-section>

     <mj-section padding-top="16px">
        <mj-column>
            <mj-divider padding-top="16px" padding-bottom="16px"/>
        </mj-column>
    </mj-section>
    <mj-section padding-top="0">
        <mj-column padding-bottom="12px">
            <mj-text font-size="14px" font-weight="600" padding="0"  padding-bottom="8px">${t(
                'Delivery Address'
            )}</mj-text>
            <mj-text font-size="13px" padding="0" padding-bottom="4px" line-height="21px">
                ${cart.firstName} ${cart.lastName}
            </mj-text>
            <mj-text font-size="13px" padding="0" line-height="21px">${deliveryAddress.address}</mj-text>
        </mj-column>

        <mj-column padding-bottom="12px">

        </mj-column>

        <mj-column padding-bottom="12px">
            <mj-text font-size="14px" font-weight="600" padding="0"  padding-bottom="8px">${t(
                'Invoice Information'
            )}</mj-text>
            <mj-text font-size="13px" padding="0" padding-bottom="4px" line-height="21px">
                ${!!cart.companyName ? cart.companyName : `${cart.firstName} ${cart.lastName}`}
            </mj-text>
            <mj-text font-size="13px" padding="0" line-height="21px">${billingAddress.address}</mj-text>
        </mj-column>
    </mj-section>
</mj-wrapper>
    `;

        await app.mail({
            from: `${store.name} <${store.email || company.email}>`,
            to: cart.email,
            subject: `${app.translate('Your Order Received')}💳`,
            noContentWrapper: true,
            template
        });
    } catch (error) {
        console.error(error.message);
    }
}

export async function updateProductsSalesCount(app, store, cart) {
    const productIds = cart.items.map(item => item.productId);
    const storeProducts = await app.collection('ecommerce.store-products').find({
        storeId: store._id,
        productId: {$in: productIds},
        $select: ['_id', 'productId', 'salesCount'],
        $disableActiveCheck: true,
        $disableSoftDelete: true
    });
    const products = await app.collection('store.products').find({
        storeId: store._id,
        $or: [{productId: {$in: productIds}}, {'variants.productId': {$in: productIds}}],
        $select: ['_id', 'productId', 'salesCount', 'variants'],
        $disableActiveCheck: true,
        $disableSoftDelete: true
    });
    const now = app.datetime.local().toJSDate();
    const storeProductsOperations = [];
    const productsOperations = [];

    for (const cartItem of cart.items) {
        const storeProduct = storeProducts.find(storeProduct => storeProduct.productId === cartItem.productId);
        storeProductsOperations.push({
            updateOne: {
                filter: {_id: storeProduct._id},
                update: {$set: {salesCount: (storeProduct.salesCount || 0) + cartItem.quantity, updatedAt: now}}
            }
        });

        for (const product of products) {
            if (product.productId === cartItem.productId) {
                productsOperations.push({
                    updateOne: {
                        filter: {_id: product._id},
                        update: {$set: {salesCount: (product.salesCount || 0) + cartItem.quantity, updatedAt: now}}
                    }
                });
            } else if (
                Array.isArray(product.variants) &&
                product.variants.some(variant => variant.productId === cartItem.productId)
            ) {
                const variantIndex = product.variants.findIndex(variant => variant.productId === cartItem.productId);

                product.variants[variantIndex].salesCount =
                    (product.variants[variantIndex].salesCount || 0) + cartItem.quantity;

                productsOperations.push({
                    updateOne: {
                        filter: {_id: product._id},
                        update: {
                            $set: {
                                salesCount: (product.salesCount || 0) + cartItem.quantity,
                                variants: product.variants,
                                updatedAt: now
                            }
                        }
                    }
                });
            }
        }
    }

    if (storeProductsOperations.length > 0) {
        await app.collection('ecommerce.store-products').bulkWrite(storeProductsOperations);
    }

    if (productsOperations.length > 0) {
        await app.collection('store.products').bulkWrite(productsOperations);
    }
}

export async function getOrder({
    app,
    company,
    store,
    cart,
    payload: orderPayload,
    now,
    user,
    customer,
    contact,
    invoiceResponsibleId,
    invoiceAddressId,
    invoiceAddress,
    deliveryReceiverId,
    deliveryAddressId,
    deliveryAddress,
    storeDeliveryWarehouse
}) {
    const numbering = await app.collection('kernel.numbering').findOne({
        code: 'salesOrderNumbering',
        $select: ['_id'],
        $disableInUseCheck: true,
        $disableActiveCheck: true,
        $disableSoftDelete: true
    });

    let order = {};

    // Get payment method.
    let paymentMethod = null;
    if (!!cart.paymentMethodId) {
        paymentMethod = store.paymentMethods.find(paymentMethod => paymentMethod.id === cart.paymentMethodId);
    }
    if (!!cart.paymentJournalId) {
        paymentMethod = store.paymentMethods.find(paymentMethod => paymentMethod.journalId === cart.paymentJournalId);
    }
    if (!!paymentMethod && paymentMethod.paymentType === 'money-order' && cart.subPaymentMethodId) {
        paymentMethod = store.paymentMethods.find(paymentMethod => paymentMethod.id === cart.subPaymentMethodId);
    }
    if (!paymentMethod) {
        throw new Error('No payment method is found!');
    }

    const paymentPolicy = await getPaymentPolicy(app, store, customer._id, orderPayload.erpUserId);

    if (
        _.isPlainObject(paymentPolicy) &&
        Array.isArray(paymentPolicy.allowedPaymentMethods) &&
        paymentPolicy.allowedPaymentMethods.length > 0 &&
        !paymentPolicy.allowedPaymentMethods.includes(paymentMethod.paymentType)
    ) {
        throw new Error('Payment method is not allowed!');
    }

    // General.
    order.module = 'ecommerce';
    order.storeId = store._id;
    order.status = store.createDraftOrder && paymentMethod.paymentType === 'open-account' ? 'draft' : 'payment-planned';
    order.code = await app.rpc('kernel.common.request-number', {
        numberingId: numbering._id,
        save: true
    });
    order.documentTypeId = store.salesDocumentTypeId;
    order.partnerId = customer._id;
    order.partnerGroupId = customer.groupId;
    order.branchId = store.branchId;
    order.currencyId = store.currencyId;
    order.currencyRate = 1;
    order.recordDate = now.toJSDate();
    order.orderDate = now.toJSDate();
    order.scheduledDate = now.toJSDate();
    order.subTotal = 0;
    order.discount = 0;
    order.discountAmount = 0;
    order.subTotalAfterDiscount = 0;
    order.taxTotal = 0;
    order.rounding = 0;
    order.grandTotal = 0;
    order.paidTotal = 0;
    order.plannedTotal = 0;
    order.appliedTaxes = [];
    order.sourceId = store.sourceId;
    order.communicationChannelId = store.communicationChannelId;
    order.invoiceResponsibleId = invoiceResponsibleId;
    order.invoiceAddressId = invoiceAddressId;
    order.invoiceAddress = invoiceAddress;
    order.deliveryReceiverId = deliveryReceiverId;
    order.deliveryAddressId = deliveryAddressId;
    order.deliveryAddress = deliveryAddress;
    order.note = cart.orderNote || '';
    order.warehouseId = store.warehouseId;
    order.paymentTermId = paymentMethod.paymentTermId;
    order.priceListId = store.priceListId || '';
    order.campaigns = cart.campaigns || [];

    if (!!cart.deliveryOptionId) {
        order.carrierId =
            store.deliveryOptions.find(deliveryOption => deliveryOption.id === cart.deliveryOptionId)?.carrierId ?? '';
    }
    if (_.isPlainObject(storeDeliveryWarehouse)) {
        order.warehouseId = storeDeliveryWarehouse._id;
        if (storeDeliveryWarehouse.branchId) order.branchId = storeDeliveryWarehouse.branchId;
        order.deliveryAddress = storeDeliveryWarehouse.address;
        deliveryAddress = storeDeliveryWarehouse.address;
        order.deliveryAddressId = '';
        deliveryAddressId = '';
    }

    if (orderPayload.erpUserId) {
        const user = await app.collection('kernel.users').findOne({
            _id: orderPayload.erpUserId,
            $select: ['partnerId'],
            $disableActiveCheck: true,
            $disableSoftDelete: true
        });
        order.salespersonId = user?.partnerId ?? '';
    }

    // Get general scheduled date.
    let scheduledDate = now;
    for (const cartItem of cart.items) {
        if (!_.isFinite(cartItem.estimatedDeliveryDuration)) {
            continue;
        }

        let date = now.plus({days: cartItem.estimatedDeliveryDuration});

        if (
            cartItem.deliveryType === 'special' &&
            (_.isDate(cartItem.deliveryDate) || _.isDate(cartItem.deliveryTime))
        ) {
            if (_.isDate(cartItem.deliveryDate)) {
                date = app.datetime.fromJSDate(cartItem.deliveryDate);
            }

            if (_.isDate(cartItem.deliveryTime)) {
                date.set({hours: cartItem.deliveryTime.getHours(), minute: cartItem.deliveryTime.getMinutes()});
            }
        }

        if (scheduledDate.toJSDate().getTime() < date.toJSDate().getTime()) {
            scheduledDate = date;
        }
    }
    order.scheduledDate = scheduledDate.toJSDate();

    // Get items.
    order.items = await getOrderItems({
        app,
        company,
        store,
        cart,
        payload: orderPayload,
        now,
        user,
        customer,
        contact,
        invoiceResponsibleId,
        invoiceAddressId,
        invoiceAddress,
        deliveryReceiverId,
        deliveryAddressId,
        deliveryAddress,
        order,
        paymentMethod
    });

    // Totals.
    let discount = 0;
    let discountAmount = 0;
    let subTotal = 0;
    let subTotalAfterDiscount = 0;
    let taxTotal = 0;
    let grandTotal = 0;
    let appliedTaxes = [];
    order.items.forEach(row => {
        subTotal += app.round(row.total, 'total');

        if (_.isPlainObject(row.taxDetail)) {
            taxTotal += app.round(row.taxDetail.taxTotal, 'total');

            row.taxDetail.applied.forEach(tax => {
                const taxIndex = _.findIndex(appliedTaxes, t => t._id === tax._id);

                if (taxIndex !== -1) {
                    appliedTaxes[taxIndex].unAppliedAmount += app.round(tax.unAppliedAmount || 0, 'total');
                    appliedTaxes[taxIndex].appliedAmount += app.round(tax.appliedAmount || 0, 'total');
                } else {
                    tax.unAppliedAmount = app.round(tax.unAppliedAmount || 0, 'total');
                    tax.appliedAmount = app.round(tax.appliedAmount || 0, 'total');
                    appliedTaxes.push(_.cloneDeep(tax));
                }
            });
        }
    });
    grandTotal = app.round(subTotal + taxTotal, 'total');
    if (Array.isArray(cart.discounts) && cart.discounts.length > 0 && grandTotal > 0 && subTotal > 0) {
        const discountsTotal = _.sumBy(
            cart.discounts.filter(d => !d.isShippingDiscount && !d.isRowDiscount),
            'amount'
        );
        const discountedSubTotal = (subTotal * (grandTotal - discountsTotal)) / grandTotal;

        discount = ((subTotal - discountedSubTotal) / subTotal) * 100;
    }
    if (_.isNumber(discount) && subTotal > 0) {
        // Get discount amount.
        discountAmount = app.round((subTotal * discount) / 100, 'total');

        // Calculate new taxes and tax total.
        const ratio = discountAmount / subTotal;
        const payload = {items: []};
        order.items.forEach(row => {
            const rowDiscountAmount = app.round(ratio * row.total, 'total');
            const newRowTotal = app.round(row.total - rowDiscountAmount, 'total');
            const key = row.taxDetail.applied.map(t => t._id).join('');
            const existingIndex = _.findIndex(payload.items, i => i.key === key);
            if (existingIndex === -1) {
                payload.items.push({
                    taxId: row.taxId,
                    quantity: row.quantity || 1,
                    taxPayload: row.taxPayload,
                    amount: newRowTotal,
                    key
                });
            } else {
                payload.items[existingIndex].amount += newRowTotal;
                payload.items[existingIndex].quantity += row.quantity || 1;
            }
        });
        const result = await app.rpc('kernel.common.calculate-taxes', payload);
        taxTotal = 0;
        for (const r of result) {
            taxTotal += app.round(r.amount, 'total');
        }
        appliedTaxes = appliedTaxes.map(tax => {
            const newTaxResult = result.find(r => r.taxId === tax._id);

            tax.appliedAmount = app.round(newTaxResult.amount, 'total');

            return tax;
        });

        // Calculate subtotal after discount.
        subTotalAfterDiscount = app.round(subTotal - discountAmount, 'total');

        // Calculate new grand total.
        grandTotal = app.round(subTotal + taxTotal - discountAmount, 'total');
    }
    order.subTotal = app.round(subTotal, 'total');
    order.discount = discount;
    order.discountAmount = app.round(discountAmount, 'total');
    order.subTotalAfterDiscount = subTotalAfterDiscount;
    order.taxTotal = app.round(taxTotal, 'total');
    order.grandTotal = app.round(grandTotal, 'total');
    order.appliedTaxes = appliedTaxes;

    // Fix grand total.
    const orderPayloadGrandTotal = parseFloat(cart.grandTotal);
    if (orderPayloadGrandTotal !== order.grandTotal) {
        const diff = orderPayloadGrandTotal - order.grandTotal;

        order.rounding = app.round(diff, 'total');
        order.grandTotal = app.round(order.grandTotal + order.rounding, 'total');
    }

    // Get payment plan.
    const {paymentTerm, paymentPlan} = await getPaymentPlan({
        app,
        company,
        store,
        cart,
        payload: orderPayload,
        now,
        user,
        customer,
        contact,
        invoiceResponsibleId,
        invoiceAddressId,
        invoiceAddress,
        deliveryReceiverId,
        deliveryAddressId,
        deliveryAddress,
        order,
        paymentMethod
    });
    order.paymentTermId = paymentTerm._id;
    order.paymentTerm = paymentTerm;
    order.paymentPlan = paymentPlan;
    order.paymentPlanningDate = paymentPlan.baseDate;

    // Initialize exchange rates.
    await (async () => {
        const currencies = await app.collection('kernel.currencies').find({
            $select: ['name']
        });
        const exchangeRates = [];
        const payloads = [];

        for (const currency of currencies) {
            if (currency.name === company.currency.name) {
                continue;
            }

            payloads.push({
                from: currency.name,
                to: company.currency.name,
                value: 1,
                options: {
                    date: order.orderDate
                }
            });
        }

        for (const payload of await app.rpc('kernel.common.convert-currencies', payloads)) {
            exchangeRates.push({
                currencyName: payload.from,
                rate: payload.rate
            });
        }

        order.exchangeRates = exchangeRates;

        const exchangeRatesMap = {};
        for (const exchangeRate of order.exchangeRates || []) {
            exchangeRatesMap[exchangeRate.currencyName] = exchangeRate.rate;
        }
        order.exchangeRatesMap = exchangeRatesMap;
    })();

    order = await app.collection('sale.orders').create(order, {user, skipEvents: true});

    if (store.createDraftOrder) {
        return order;
    }

    // Persist campaigns.
    if (Array.isArray(cart.campaigns) && cart.campaigns.length > 0) {
        const ts = [];

        for (const campaign of cart.campaigns) {
            const t = {};

            t.date = app.datetime.local().toJSDate();
            t.documentId = order._id;
            t.documentCode = order.code;
            t.documentCollection = 'sale.orders';
            t.documentView = 'ecommerce.sales.orders';
            t.documentTitle = app.translate('Sale Orders');
            t.partnerId = customer._id;
            t.partnerCode = customer.code;
            t.partnerName = customer.name;
            t.campaignId = campaign.campaignId;
            t.campaignCode = campaign.campaignCode;
            t.campaignName = campaign.campaignName;
            t.campaignType = campaign.campaignType;
            t.campaignApplicationType = campaign.campaignApplicationType;
            t.couponCode = campaign.couponCode;
            t.itemId = campaign.itemId;
            t.productId = campaign.productId;
            t.productCode = campaign.productCode;
            t.productDefinition = campaign.productDefinition;
            t.rewardValueType = campaign.rewardValueType;
            t.rewardValue = campaign.rewardValue;
            t.discount = campaign.discount;
            t.discountAmount = campaign.discountAmount;

            ts.push(t);
        }

        await app.collection('sale.campaign-transactions').create(ts);
    }

    if (app.setting('sale.sellout') && store.useSellout) {
        try {
            const items = order.items.flatMap(item => {
                const selloutItems = [];

                if (item.selloutId) {
                    selloutItems.push(
                        _.pick(item, [
                            'productId',
                            'productCode',
                            'productDefinition',
                            'unitId',
                            'quantity',
                            'selloutId',
                            'selloutCode',
                            'selloutName',
                            'selloutAmount',
                            'total'
                        ])
                    );
                }

                if (Array.isArray(item.subItems) && item.subItems.length > 0) {
                    selloutItems.push(
                        ...item.subItems
                            .filter(subItem => subItem.selloutId)
                            .map(subItem =>
                                _.pick(subItem, [
                                    'productId',
                                    'productCode',
                                    'productDefinition',
                                    'unitId',
                                    'quantity',
                                    'selloutId',
                                    'selloutCode',
                                    'selloutName',
                                    'selloutAmount'
                                ])
                            )
                    );
                }

                return selloutItems;
            });

            if (items.length > 0) {
                await app.rpc('sale.sellout-record-transactions', {
                    documentType: 'order',
                    documentId: order._id,
                    documentCollection: 'sale.orders',
                    documentView: 'sale.sales.orders-detail',
                    documentTitle: app.translate('Sale Order'),
                    documentCode: order.code,
                    documentCurrencyId: order.currencyId,
                    documentCurrencyRate: order.currencyRate ?? 1,
                    items
                });
            }
        } catch (error) {
            console.log('Sellout transaction error in [approve-payment] ->', error.message);
        }
    }

    // Initialize profits.
    await app.rpc('sale.update-document-profits', {
        profitBase: app.setting('sale.profitBase'),
        documentId: order._id,
        documentCollection: 'sale.orders',
        currencyId: order.currencyId,
        currencyRate: order.currencyRate,
        date: order.orderDate,
        exchangeRates: order.exchangeRates ?? [],
        items: order.items.map(item => ({
            productId: item.productId,
            warehouseId: item.warehouseId,
            quantity: item.quantity,
            unitId: item.unitId,
            freight: item.freight || 0,
            totalSalesPrice: item.realTotal
        }))
    });

    // Initialize extras.
    const extra = {
        basePrice: 0,
        grossProfit: 0,
        profitRate: 0,
        profitMargin: 0,
        cashAmount: 0,
        cashInstallmentCount: 0,
        moneyTransferAmount: 0,
        moneyTransferInstallmentCount: 0,
        chequeAmount: 0,
        chequeInstallmentCount: 0,
        promissoryNoteAmount: 0,
        promissoryNoteInstallmentCount: 0,
        posAmount: 0,
        posInstallmentCount: 0,
        installmentCount: 0
    };
    if (_.isPlainObject(order.paymentPlan) && !_.isEmpty(order.paymentPlan)) {
        const paymentPlan = order.paymentPlan;
        let installmentCount = 0;

        for (const i of paymentPlan.items || []) {
            if (!_.isNumber(extra[`${i.documentType}Amount`])) extra[`${i.documentType}Amount`] = 0;
            if (!_.isNumber(extra[`${i.documentType}InstallmentCount`])) extra[`${i.documentType}InstallmentCount`] = 0;
            if (!_.isNumber(extra[`${i.documentType}Amount`])) extra[`${i.documentType}Amount`] = 0;

            extra[`${i.documentType}Amount`] += i.total;

            if (i.documentType === 'pos') {
                extra[`${i.documentType}InstallmentCount`] += i.installmentCount;

                installmentCount += i.installmentCount;
            } else {
                extra[`${i.documentType}InstallmentCount`]++;

                installmentCount++;
            }
        }

        for (const documentType of Object.keys(_.groupBy(paymentPlan.items || [], 'documentType'))) {
            if (_.isNumber(extra[`${documentType}Amount`])) {
                extra[`${documentType}Amount`] = app.round(extra[`${documentType}Amount`], 'currency');
            }
        }

        if (installmentCount > 0) {
            extra.installmentCount = installmentCount;
        }
    }
    const profit = await app.collection('sale.document-profits').findOne({
        documentId: order._id,
        documentCollection: 'sale.orders',
        $select: ['totalBasePrice', 'totalGrossProfit', 'profitRate', 'profitMargin']
    });
    if (_.isPlainObject(profit)) {
        extra.basePrice = profit.totalBasePrice;
        extra.grossProfit = profit.totalGrossProfit;
        extra.profitRate = profit.profitRate;
        extra.profitMargin = profit.profitMargin;
    }

    // Approve order.
    order = await app.collection('sale.orders').patch(
        {_id: order._id},
        {
            status: 'approved',
            extra
        }
    );

    // Create payments.
    if (paymentMethod.paymentType !== 'open-account') {
        await app.rpc('sale.create-order-receipts', [order._id], {user});
    }
    if (paymentMethod.paymentType === 'credit-card') {
        order = await app.collection('sale.orders').get(order._id);

        for (const entryId of order.financialEntryIds || []) {
            await app.rpc('finance.approve-entry', entryId, {user});
        }

        if (!!orderPayload.paymentIntentCode && (order.financialEntryIds || []).length > 0) {
            const entryId = order.financialEntryIds[0];
            const entry = await app.collection('finance.entries').findOne({
                _id: entryId,
                $select: ['_id', 'reference']
            });

            if (!!entry) {
                await app.collection('finance.online-pos-receipts').patch(
                    {code: orderPayload.paymentIntentCode},
                    {
                        partnerId: order.partnerId,
                        referenceId: entryId,
                        referenceCode: entry.code,
                        referenceCollection: 'finance.entries',
                        referenceView: 'finance.receivable.receipts'
                    }
                );
            }
        }

        setTimeout(() => {
            app.collection('finance.receipts').patch({_id: order.receiptId}, {status: 'closed'}, {user});
        }, 2000);
    }

    // Create partner limit.
    await (async () => {
        const partner = customer;

        if (partner.enableLimitChecks && partner.limitControlDocument === 'order') {
            const limit = {};

            limit.partnerType = 'customer';
            limit.partnerId = partner._id;
            limit.guaranteeId = order.guaranteeId;
            limit.date = app.datetime.local().toJSDate();
            limit.documentCollection = 'sale.orders';
            limit.documentView = 'sale.sales.orders-detail';
            limit.documentId = order._id;
            limit.currencyId = partner.currencyId;
            limit.amount = -order.grandTotal;

            if (order.currencyId !== partner.currencyId) {
                const documentCurrency = await app.collection('kernel.currencies').findOne({
                    _id: order.currencyId,
                    $select: ['name'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });
                const partnerCurrency = await app.collection('kernel.currencies').findOne({
                    _id: partner.currencyId,
                    $select: ['name'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });
                limit.amount = -(await app.rpc('kernel.common.convert-currency', {
                    from: documentCurrency.name,
                    to: partnerCurrency.name,
                    value: order.grandTotal,
                    options: {
                        date: order.orderDate
                    }
                }));
            }

            await app.collection('finance.partner-limit-transactions').create(limit);
        }
    })();

    try {
        // Create external reservations.
        await (async () => {
            const items = [];

            for (const item of order.items) {
                if (item.productType === 'service') continue;

                if (Array.isArray(item.subItems) && item.subItems.length > 0) {
                    const productIds = item.subItems.map(subItem => subItem.productId);
                    const products = await app.collection('inventory.products').find({
                        _id: {$in: productIds},
                        $disableActiveCheck: true,
                        $disableSoftDelete: true
                    });
                    const productMap = _.keyBy(products, '_id');

                    for (const subItem of item.subItems) {
                        const product = productMap[subItem.productId];
                        if (!product) continue;

                        const row = {};

                        row.productId = product._id;
                        row.productType = item.type;
                        row.date = item.scheduledDate;
                        row.warehouseId = item.warehouseId;
                        row.quantity = subItem.quantity;
                        row.unitId = item.unitId;
                        row.unitPrice = subItem.unitPrice;

                        if (app.hasModule('pcm') && !!item.pcmHash) {
                            row.pcmHash = item.pcmHash;
                        }

                        items.push(row);
                    }
                } else {
                    const row = {};

                    row.productId = item.productId;
                    row.productType = item.productType;
                    row.date = item.scheduledDate;
                    row.warehouseId = item.warehouseId;
                    row.quantity = item.quantity;
                    row.unitId = item.unitId;
                    row.unitPrice = (item.realTotal / item.quantity) * (order.currencyRate || 1);

                    if (app.hasModule('pcm') && !!item.pcmHash) {
                        row.pcmModelId = item.pcmModelId;
                        row.pcmConfigurationId = item.pcmConfigurationId;
                        row.pcmHash = item.pcmHash;
                    }

                    items.push(row);
                }
            }

            await app.rpc(
                'inventory.create-external-reservations',
                {
                    documentId: order._id,
                    documentCollection: 'sale.orders',
                    documentCode: order.code,
                    documentView: 'sale.sales.orders',
                    documentTitle: 'Sale Orders',
                    type: 'outgoing',
                    code: order.code,
                    partnerId: order.partnerId,
                    orderDate: order.orderDate,
                    exchangeRatesMap: order.exchangeRatesMap || {},
                    items,
                    checkProductStocks: false,
                    disableAdvancedDeliveryPlanning: true
                },
                {user}
            );
        })();

        // Create transfer.
        await app.rpc('sale.create-deliveries', {orderIds: [order._id]});

        // Create invoice.
        if (store.automaticInvoice) {
            await app.rpc('sale.create-invoices', {orderIds: [order._id]});

            const country = await app.collection('kernel.countries').findOne({
                _id: order.invoiceAddress?.countryId,
                $select: ['code'],
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });

            if (country && country.code !== 'TR') {
                const ecommerceOrder = await app.collection('sale.orders').findOne({
                    _id: order._id,
                    $select: ['relatedDocuments'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });

                const rd = ecommerceOrder.relatedDocuments.find(rd => rd.collection === 'accounting.customer-invoices');

                if (rd && Array.isArray(rd.ids) && rd.ids.length > 0) {
                    await app.collection('accounting.customer-invoices').bulkWrite([
                        {
                            updateOne: {
                                filter: {_id: rd.ids[0]},
                                update: {
                                    $set: {
                                        ...(store.exportInvoiceScenario && {
                                            invoiceScenario: store.exportInvoiceScenario
                                        }),
                                        ...(store.exportEInvoiceTypeId && {
                                            eInvoiceTypeId: store.exportEInvoiceTypeId
                                        }),
                                        ...(store.exportEInvoiceTypeConditionCode && {
                                            eInvoiceTypeConditionCode: store.exportEInvoiceTypeConditionCode
                                        })
                                    }
                                }
                            }
                        }
                    ]);
                }
            }
        }
    } catch (error) {}

    return order;
}

export async function getOrderItems({
    app,
    company,
    store,
    cart,
    payload: orderPayload,
    now,
    user,
    customer,
    contact,
    invoiceResponsibleId,
    invoiceAddressId,
    invoiceAddress,
    deliveryReceiverId,
    deliveryAddressId,
    deliveryAddress,
    order,
    paymentMethod
}) {
    // const finalItems = [];
    // for (const item of cart.items) {
    //     if (Array.isArray(item.subItems) && item.subItems.length > 0) {
    //     } else {
    //         finalItems.push(item);
    //     }
    // }

    const uniqueProductIds = _.uniq(cart.items.map(item => item.productId));
    const products = await app.collection('inventory.products').find({
        _id: {$in: uniqueProductIds},
        $select: ['_id', 'displayName', 'nameTranslations', 'salesNote', 'variations'],
        $disableSoftDelete: true,
        $disableActiveCheck: true
    });
    const storeProducts = await app.collection('ecommerce.store-products').find({
        productId: {$in: uniqueProductIds},
        storeId: store._id,
        $select: [
            'productId',
            'productCode',
            'productDefinition',
            'productBarcode',
            'taxApplication',
            'taxId',
            'salesPrice',
            'discount'
        ],
        $disableSoftDelete: true,
        $disableActiveCheck: true
    });
    const taxes = await app.collection('kernel.taxes').find({
        _id: {$in: _.uniq(storeProducts.map(storeProduct => storeProduct.taxId))},
        $select: ['_id', 'amount'],
        $disableSoftDelete: true,
        $disableActiveCheck: true
    });
    const pcItems = await app.collection('purchase.procurement-catalog').find({
        productId: {$in: uniqueProductIds}
    });
    let items = [];

    const selloutItemMap = {};
    if (app.setting('sale.sellout') && store.useSellout) {
        const selloutItems = await app.collection('sale.sellout-items').find({
            status: 'published',
            validFrom: {$lte: now},
            validTo: {$gte: now},
            productId: {$in: uniqueProductIds}
        });

        for (const selloutItem of selloutItems) {
            selloutItemMap[selloutItem.productId] = selloutItem;
        }
    }

    // const pricePolicy = await getPricePolicy(app, store, cart.customerId);

    // const policyPrices = _.isPlainObject(pricePolicy)
    //     ? await getPolicyPrices(
    //           app,
    //           store,
    //           pricePolicy,
    //           cart.items.map(item => item.productId)
    //       )
    //     : null;

    // Cart items.
    for (const cartItem of cart.items) {
        const product = products.find(product => product._id === cartItem.productId);
        const storeProduct = storeProducts.find(storeProduct => storeProduct.productId === cartItem.productId);
        const tax = taxes.find(tax => tax._id === storeProduct.taxId);
        const pcItem = pcItems.find(pcItem => pcItem.productId === cartItem.productId);

        let item = {
            id: Random.id(16),
            productId: cartItem.productId,
            productCode: storeProduct.productCode,
            productDefinition: storeProduct.productDefinition,
            productType: 'stockable',
            barcode: storeProduct.productBarcode || '',
            branchId: order.branchId,
            warehouseId: cartItem.warehouseId || order.warehouseId,
            quantity: cartItem.quantity,
            unitId: cartItem.unitId,
            baseUnitId: cartItem.unitId,
            baseQuantity: cartItem.quantity,
            unitPrice: 0,
            grossUnitPrice: 0,
            discount: 0,
            unitPriceAfterDiscount: 0,
            grossUnitPriceAfterDiscount: 0,
            taxId: storeProduct.taxId,
            taxTotal: 0,
            grossTotal: 0,
            stockQuantity: 0,
            orderedQuantity: 0,
            assignedQuantity: 0,
            availableQuantity: 0,
            warehouseStockQuantity: 0,
            warehouseOrderedQuantity: 0,
            warehouseAssignedQuantity: 0,
            warehouseAvailableQuantity: 0,
            total: 0,
            partnerId: customer._id,
            invoiceResponsibleId,
            invoiceAddressId,
            invoiceAddress,
            deliveryReceiverId,
            deliveryAddressId,
            deliveryAddress
        };

        let note = (product.salesNote || '').trim();
        item.description = !!note ? product.displayName + ' ' + note : product.displayName;
        if (Array.isArray(product.nameTranslations) && product.nameTranslations.length > 0) {
            const nt = product.nameTranslations.find(nt => nt.languageId === customer.languageId);

            if (!!nt && !!nt.translation) {
                item.description = !!note
                    ? `${product.code} - ${nt.translation}` + ' ' + note
                    : `${product.code} - ${nt.translation}`;
            }
        }

        if (!!store.evaluateVariations && Array.isArray(product.variations) && product.variations.length > 0) {
            let note = (product.salesNote || '').trim();

            item.productCode = cartItem.productCode;
            item.productDefinition = cartItem.productName;
            item.description = !!note
                ? `${cartItem.productCode} ${cartItem.productName} ${note}`
                : `${cartItem.productCode} ${cartItem.productName}`;
        }

        item.scheduledDate = _.isFinite(cartItem.estimatedDeliveryDuration)
            ? now.plus({days: cartItem.estimatedDeliveryDuration || 3})
            : now;
        if (
            cartItem.deliveryType === 'special' &&
            (_.isDate(cartItem.deliveryDate) || _.isDate(cartItem.deliveryTime))
        ) {
            if (_.isDate(cartItem.deliveryDate)) {
                item.scheduledDate = app.datetime.fromJSDate(cartItem.deliveryDate);
            }

            if (_.isDate(cartItem.deliveryTime)) {
                item.scheduledDate.set({
                    hours: cartItem.deliveryTime.getHours(),
                    minute: cartItem.deliveryTime.getMinutes()
                });
            }
        }
        item.scheduledDate = item.scheduledDate.toJSDate();

        // Vendor product.
        if (!!pcItem) {
            item.vendorId = pcItem.vendorId;

            if (Array.isArray(pcItem.warehouses) && pcItem.warehouses.length > 0) {
                item.warehouseId = pcItem.warehouses[0].warehouseId;
            }
        }

        item.unitPrice = storeProduct.salesPrice;

        if (app.setting('sale.sellout') && store.useSellout) {
            const selloutItem = selloutItemMap[item.productId];
            const selloutAmount = await calculateSelloutAmount(app, store, selloutItem, {
                price: item.unitPrice,
                quantity: item.quantity
            });

            if (_.isFinite(selloutAmount)) {
                item.unitPrice = app.round(item.unitPrice - selloutAmount, 'unit-price');
                item.selloutId = selloutItem.selloutId;
                item.selloutCode = selloutItem.selloutCode;
                item.selloutName = selloutItem.selloutName;
                item.selloutAmount = selloutAmount;
            }
        }

        if (storeProduct.taxApplication !== 'tax-excluded') {
            item.unitPrice = app.round(item.unitPrice / (1 + tax.amount / 100), 'unit-price');
        }

        // PCM
        if (cartItem.isPCMProduct && !!cartItem.pcmPayload && Object.keys(cartItem.pcmPayload).length > 0) {
            const pcmPayload = cartItem.pcmPayload;
            if (typeof pcmPayload.deliveryDate === 'string' && pcmPayload.deliveryDate.length > 0) {
                pcmPayload.deliveryDate = new Date(Date.parse(pcmPayload.deliveryDate));
            }

            const model = await app.collection('pcm.models').findOne({
                _id: pcmPayload.model.id,
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });
            const hash = hasher([model.productId, JSON.stringify(pcmPayload.values)]);
            const data = {
                modelId: model._id,
                productId: model.productId,
                payload: {
                    ..._.pick(pcmPayload, ['stepId', 'values', 'summary', 'price', 'quantity', 'deliveryDate'])
                },
                hash
            };
            const configuration = await app.collection('pcm.configurations').create(data);

            let description = model.name;
            for (const item of (configuration.payload.summary || {}).items || []) {
                description += `\n- ${item.label}: ${item.value}`;
            }

            item.description = description;
            item.pcmModelId = model._id;
            item.pcmConfigurationId = configuration._id;
            item.pcmHash = hash;
            item.scheduledDate = pcmPayload.deliveryDate;
            item.quantity = pcmPayload.quantity;
            item.baseQuantity = pcmPayload.quantity;
            item.unitPrice = pcmPayload.quantity > 0 ? pcmPayload.price / pcmPayload.quantity : 0;
            if (storeProduct.taxApplication !== 'tax-excluded') {
                item.unitPrice = app.round(item.unitPrice / (1 + tax.amount / 100), 'unit-price');
            }
        }

        // Kit
        if (cartItem.isKitProduct) {
            const configuration = await app.collection('pcm.configurations').findOne({
                productId: cartItem.productId,
                isPreMade: true,
                $select: ['_id']
            });

            if (!configuration) {
                if (Array.isArray(cartItem.subItems) && cartItem.subItems.length > 0) {
                    const kitProductIds = cartItem.subItems.map(sp => sp.productId);

                    const now = app.datetime.local().toJSDate();
                    const exchangeRatesMap = {};
                    const productPricesMap = {};
                    const productPrices = (
                        await app.collection('sale.product-prices').find({
                            productId: {$in: kitProductIds},
                            priceListId: store.priceListId,
                            validFrom: {$lte: now},
                            validTo: {$gte: now}
                        })
                    ).filter(p => p.min <= 1 || _.isUndefined(p.min));
                    for (const productPrice of productPrices) {
                        let price = productPrice.price ?? 0;

                        if (productPrice.currencyId !== store.currencyId) {
                            const exchangeRatesMapKey = `${productPrice.currencyId}${store.currencyId}`;

                            if (_.isFinite(exchangeRatesMap[exchangeRatesMapKey])) {
                                price *= exchangeRatesMap[exchangeRatesMapKey];
                            } else {
                                const rate = await app.rpc('ecommerce.convert-currency-for-store', {
                                    storeId: store._id,
                                    currencyId: productPrice.currencyId
                                });

                                exchangeRatesMap[exchangeRatesMapKey] = rate;

                                price *= rate;
                            }

                            price = app.round(price, 'unit-price');
                        }

                        productPricesMap[productPrice.productId] = price;
                    }

                    const kitProduct = await app.collection('inventory.products').findOne({
                        _id: cartItem.productId,
                        $select: ['subProducts'],
                        $disableActiveCheck: true,
                        $disableSoftDelete: true
                    });

                    const selloutItemMap = {};
                    if (app.setting('sale.sellout') && store.useSellout) {
                        const selloutItems = await app.collection('sale.sellout-items').find({
                            status: 'published',
                            validFrom: {$lte: now},
                            validTo: {$gte: now},
                            productId: {$in: kitProductIds}
                        });

                        for (const selloutItem of selloutItems) {
                            selloutItemMap[selloutItem.productId] = selloutItem;
                        }
                    }

                    const kitExchangeRatesMap = {};
                    let unitPrice = 0;
                    for (const sp of cartItem.subItems) {
                        let price = productPricesMap[sp.productId] ?? 0;

                        if (
                            _.isPlainObject(kitProduct) &&
                            Array.isArray(kitProduct.subProducts) &&
                            kitProduct.subProducts.length > 0
                        ) {
                            const product =
                                _.find(kitProduct.subProducts, {productId: sp.productId}) ||
                                _.find(_.flatMap(kitProduct.subProducts, 'subProductAlternates'), {
                                    productId: sp.productId
                                });

                            if (product && _.isFinite(product.price) && product.price > 0) {
                                price = product.price;

                                if (product.currencyId && product.currencyId !== store.currencyId) {
                                    const kitExchangeRatesMapKey = `${product.currencyId}${store.currencyId}`;

                                    if (_.isFinite(kitExchangeRatesMap[kitExchangeRatesMapKey])) {
                                        price *= kitExchangeRatesMap[kitExchangeRatesMapKey];
                                    } else {
                                        const rate = await app.rpc('ecommerce.convert-currency-for-store', {
                                            storeId: store._id,
                                            currencyId: product.currencyId
                                        });

                                        kitExchangeRatesMap[kitExchangeRatesMapKey] = rate;
                                        price *= rate;
                                    }

                                    price = app.round(price, 'unit-price');
                                }
                            }
                        }

                        if (app.setting('sale.sellout') && store.useSellout) {
                            const selloutItem = selloutItemMap[sp.productId];
                            const selloutAmount = await calculateSelloutAmount(app, store, selloutItem, {
                                price: price,
                                quantity: sp.quantity,
                                type: 'kit'
                            });

                            if (_.isFinite(selloutAmount)) {
                                price = app.round(price - selloutAmount, 'total');
                                sp.selloutId = selloutItem.selloutId;
                                sp.selloutCode = selloutItem.selloutCode;
                                sp.selloutName = selloutItem.selloutName;
                                sp.selloutAmount = selloutAmount;
                            }
                        }

                        sp.unitPrice = price;
                        unitPrice += price * sp.quantity;
                    }

                    item.subItems = cartItem.subItems;
                    item.unitPrice = unitPrice;
                }
            }
        }

        // if (_.isPlainObject(policyPrices)) {
        //     const {productPricesMap, discountedProductPricesMap} = policyPrices;
        //
        //     const price = productPricesMap[cartItem.productId];
        //
        //     if (typeof price === 'number') {
        //         item.unitPrice = price;
        //     }
        //
        //     const discountedPrice = discountedProductPricesMap[cartItem.productId];
        //
        //     if (typeof discountedPrice === 'number') {
        //         item.unitPrice = discountedPrice;
        //     }
        // }

        if (typeof cartItem.discountedPrice === 'number' && typeof cartItem.price === 'number' && cartItem.price > 0) {
            let cartPrice = cartItem.price;
            // if (storeProduct.taxApplication !== 'tax-excluded') {
            //     cartPrice = app.round(cartPrice / (1 + tax.amount / 100), 'unit-price');
            // }

            item.discount = ((cartPrice - cartItem.discountedPrice) / cartPrice) * 100;
        } else if (_.isNumber(storeProduct.discount) && storeProduct.discount > 0) {
            item.discount = storeProduct.discount;
        }

        // Stock quantity.
        const stockReport = await app.rpc('inventory.get-stock-report', {
            date: item.scheduledDate || app.datetime.local().toJSDate(),
            productId: item.productId
        });
        if (Array.isArray(stockReport) && stockReport.length > 0) {
            const r = stockReport[0];

            item.stockQuantity = r.stockQuantity;
            item.orderedQuantity = r.orderedQuantity;
            item.assignedQuantity = r.assignedQuantity;
            item.availableQuantity = r.availableQuantity;
        }
        const warehouseReport = await app.rpc('inventory.get-stock-report', {
            date: item.scheduledDate || app.datetime.local().toJSDate(),
            productId: item.productId,
            warehouseId: item.warehouseId
        });
        if (Array.isArray(warehouseReport) && warehouseReport.length > 0) {
            const r = warehouseReport[0];

            item.warehouseStockQuantity = r.stockQuantity;
            item.warehouseOrderedQuantity = r.orderedQuantity;
            item.warehouseAssignedQuantity = r.assignedQuantity;
            item.warehouseAvailableQuantity = r.availableQuantity;
        }

        // Evaluate surplus goods if health module is enabled.
        if (app.hasModule('health')) {
            const res = await app.rpc(
                'health.surplus-goods-decorate-bd-row',
                {
                    row: fastCopy(item),
                    date: order.orderDate,
                    partnerId: customer._id
                },
                {user}
            );

            item.surplusGoodDescription = res.surplusGoodDescription || '';
            item.hasSurplusGood = res.hasSurplusGood;
        }

        item = await app.rpc(
            'sale.calculate-order-row-totals',
            {
                row: item,
                field: 'unitPrice',
                model: order
            },
            {user}
        );

        items.push(item);
    }

    // Evaluate due difference.
    if (_.isNumber(cart.dueDifference) && cart.dueDifference > 0) {
        const dueDifference = cart.dueDifference;
        const grossTotal = _.sumBy(items, 'grossTotal');
        const newItems = [];

        for (const item of items) {
            const ratio = item.grossTotal / grossTotal;
            const diff = dueDifference * ratio;

            if (diff > 0) {
                newItems.push(
                    await app.rpc(
                        'sale.calculate-order-row-totals',
                        {
                            row: {
                                ...item,
                                grossTotal: app.round(item.grossTotal + diff, 'total')
                            },
                            field: 'grossTotal',
                            model: order
                        },
                        {user}
                    )
                );
            } else {
                newItems.push(item);
            }
        }

        items = newItems;
    }

    // Delivery items.
    if (cart.deliveryTotal > 0) {
        const itemThatHasDeliveryOptionId = cart.items.find(i => !!i.deliveryOptionId);
        if (!!itemThatHasDeliveryOptionId) {
            let deliveryOption = null;
            if (!!cart.deliveryOptionId) {
                deliveryOption = store.deliveryOptions.find(
                    deliveryOption => deliveryOption.id === cart.deliveryOptionId
                );
            } else {
                deliveryOption = store.deliveryOptions.find(
                    deliveryOption => deliveryOption.id === itemThatHasDeliveryOptionId.deliveryOptionId
                );
            }

            if (!!deliveryOption && !_.isEmpty(deliveryOption.serviceProductId)) {
                const product = await app.collection('inventory.products').findOne({
                    _id: deliveryOption.serviceProductId,
                    $select: [
                        '_id',
                        'code',
                        'definition',
                        'type',
                        'displayName',
                        'barcode',
                        'baseUnitId',
                        'nameTranslations',
                        'salesNote'
                    ],
                    $disableSoftDelete: true,
                    $disableActiveCheck: true
                });

                let unitPrice = app.round(cart.deliveryTotal, 'unit-price');
                if (!!deliveryOption.taxId) {
                    const tax = await app.collection('kernel.taxes').findOne({
                        _id: deliveryOption.taxId,
                        $select: ['_id', 'amount'],
                        $disableSoftDelete: true,
                        $disableActiveCheck: true
                    });

                    unitPrice = app.round(unitPrice / (1 + tax.amount / 100), 'unit-price');
                }

                let item = {
                    id: Random.id(16),
                    productId: product._id,
                    productCode: product.code,
                    productDefinition: product.definition,
                    productType: product.type,
                    barcode: product.barcode || '',
                    scheduledDate: order.scheduledDate,
                    branchId: order.branchId,
                    warehouseId: order.warehouseId,
                    quantity: 1,
                    unitId: product.baseUnitId,
                    baseUnitId: product.baseUnitId,
                    baseQuantity: 1,
                    unitPrice,
                    grossUnitPrice: 0,
                    discount: 0,
                    unitPriceAfterDiscount: 0,
                    grossUnitPriceAfterDiscount: 0,
                    taxId: deliveryOption.taxId,
                    taxTotal: 0,
                    grossTotal: 0,
                    stockQuantity: 0,
                    orderedQuantity: 0,
                    assignedQuantity: 0,
                    availableQuantity: 0,
                    warehouseStockQuantity: 0,
                    warehouseOrderedQuantity: 0,
                    warehouseAssignedQuantity: 0,
                    warehouseAvailableQuantity: 0,
                    total: 0,
                    partnerId: customer._id,
                    invoiceResponsibleId,
                    invoiceAddressId,
                    invoiceAddress,
                    deliveryReceiverId,
                    deliveryAddressId,
                    deliveryAddress
                };

                if (Array.isArray(cart.discounts) && cart.discounts.length > 0) {
                    const shippingDiscount = cart.discounts.find(d => !!d.isShippingDiscount);

                    if (!!shippingDiscount) {
                        item.discount = shippingDiscount.percentage || 0;
                    }
                }

                let note = (product.salesNote || '').trim();
                item.description = !!note ? product.displayName + ' ' + note : product.displayName;
                if (Array.isArray(product.nameTranslations) && product.nameTranslations.length > 0) {
                    const nt = product.nameTranslations.find(nt => nt.languageId === customer.languageId);

                    if (!!nt && !!nt.translation) {
                        item.description = !!note
                            ? `${product.code} - ${nt.translation}` + ' ' + note
                            : `${product.code} - ${nt.translation}`;
                    }
                }

                item = await app.rpc(
                    'sale.calculate-order-row-totals',
                    {
                        row: item,
                        field: 'unitPrice',
                        model: order
                    },
                    {user}
                );

                items.push(item);
            }
        }
    }

    // Cash on delivery service cost.
    if (
        paymentMethod.paymentType === 'cash-on-delivery' &&
        !!paymentMethod.serviceProductId &&
        !!paymentMethod.taxId &&
        paymentMethod.cashOnDeliveryServiceFee > 0
    ) {
        const product = await app.collection('inventory.products').findOne({
            _id: paymentMethod.serviceProductId,
            $select: [
                '_id',
                'code',
                'definition',
                'type',
                'displayName',
                'barcode',
                'baseUnitId',
                'nameTranslations',
                'salesNote'
            ],
            $disableSoftDelete: true,
            $disableActiveCheck: true
        });
        let item = {
            id: Random.id(16),
            productId: product._id,
            productCode: product.code,
            productDefinition: product.definition,
            productType: product.type,
            barcode: product.barcode || '',
            scheduledDate: order.scheduledDate,
            branchId: order.branchId,
            warehouseId: order.warehouseId,
            quantity: 1,
            unitId: product.baseUnitId,
            baseUnitId: product.baseUnitId,
            baseQuantity: 1,
            unitPrice: paymentMethod.cashOnDeliveryServiceFee,
            grossUnitPrice: 0,
            discount: 0,
            unitPriceAfterDiscount: 0,
            grossUnitPriceAfterDiscount: 0,
            taxId: paymentMethod.taxId,
            taxTotal: 0,
            grossTotal: 0,
            stockQuantity: 0,
            orderedQuantity: 0,
            assignedQuantity: 0,
            availableQuantity: 0,
            warehouseStockQuantity: 0,
            warehouseOrderedQuantity: 0,
            warehouseAssignedQuantity: 0,
            warehouseAvailableQuantity: 0,
            total: 0,
            partnerId: customer._id,
            invoiceResponsibleId,
            invoiceAddressId,
            invoiceAddress,
            deliveryReceiverId,
            deliveryAddressId,
            deliveryAddress
        };

        let note = (product.salesNote || '').trim();
        item.description = !!note ? product.displayName + ' ' + note : product.displayName;
        if (Array.isArray(product.nameTranslations) && product.nameTranslations.length > 0) {
            const nt = product.nameTranslations.find(nt => nt.languageId === customer.languageId);

            if (!!nt && !!nt.translation) {
                item.description = !!note
                    ? `${product.code} - ${nt.translation}` + ' ' + note
                    : `${product.code} - ${nt.translation}`;
            }
        }

        item = await app.rpc(
            'sale.calculate-order-row-totals',
            {
                row: item,
                field: 'unitPrice',
                model: order
            },
            {user}
        );

        items.push(item);
    }

    return items;
}

export async function getPaymentPlan({
    app,
    company,
    store,
    cart,
    payload: orderPayload,
    now,
    user,
    customer,
    contact,
    invoiceResponsibleId,
    invoiceAddressId,
    invoiceAddress,
    deliveryReceiverId,
    deliveryAddressId,
    deliveryAddress,
    order,
    paymentMethod
}) {
    const currencyPrecision = app.setting('system.currencyPrecision');
    const percentagePrecision = app.setting('system.percentagePrecision');
    const round = app.roundNumber;

    let paymentTerm = null;
    if (!!paymentMethod.paymentTermId) {
        paymentTerm = await app.collection('finance.payment-terms').findOne({
            _id: paymentMethod.paymentTermId
        });
    } else {
        paymentTerm = await app.collection('finance.payment-terms').findOne({
            system: true
        });

        return {paymentTerm, paymentPlan: {}};
    }

    if (paymentMethod.paymentType === 'open-account') {
        return {paymentTerm, paymentPlan: {}};
    }

    const report = {
        baseTotal: 0,
        difference: 0,
        differencePercentage: 0,
        total: 0,
        changing: 0,
        openAmount: 0,
        dueDate: app.datetime.local().startOf('day').toJSDate(),
        dueDayDifference: 0,
        avgDueDate: app.datetime.local().startOf('day').toJSDate(),
        avgDueDayDifference: 0
    };
    const sectionReport = {};

    function findBaseDate(section, forcedDate = null) {
        const sectionTerm = paymentTerm[section];
        let baseDate = now.startOf('day').toJSDate();

        if (_.isNull(forcedDate)) {
            if (paymentTerm.dueDateBase === 'issueDate') {
                baseDate = order.orderDate;
            } else if (paymentTerm.dueDateBase === 'recordDate') {
                baseDate = order.recordDate;
            }
        } else {
            baseDate = forcedDate;
        }
        baseDate = app.datetime.fromJSDate(baseDate).startOf('day').toJSDate();

        if (forcedDate) {
            if (_.isNumber(sectionTerm.startDateMonths) && sectionTerm.startDateMonths > 0) {
                baseDate = app.datetime.fromJSDate(baseDate).plus({months: sectionTerm.startDateMonths}).toJSDate();
            }

            if (sectionTerm.startDateMonthPosition === 'start') {
                const date = app.datetime.fromJSDate(baseDate).startOf('month');

                if (app.datetime.fromJSDate(baseDate) > date) {
                    baseDate = date.plus({months: 1}).toJSDate();
                } else {
                    baseDate = date.toJSDate();
                }
            } else if (sectionTerm.startDateMonthPosition === 'middle') {
                const date = app.datetime.fromJSDate(baseDate).startOf('month').plus({days: 15});

                if (app.datetime.fromJSDate(baseDate) > date) {
                    baseDate = date.endOf('month').toJSDate();
                } else {
                    baseDate = date.toJSDate();
                }
            } else if (sectionTerm.startDateMonthPosition === 'end') {
                baseDate = app.datetime.fromJSDate(baseDate).endOf('month').toJSDate();
            }

            if (_.isNumber(sectionTerm.startDateDays) && sectionTerm.startDateDays > 0) {
                baseDate = app.datetime.fromJSDate(baseDate).plus({days: sectionTerm.startDateDays}).toJSDate();
            }

            if (_.isNumber(sectionTerm.toleranceDays) && sectionTerm.toleranceDays > 0) {
                baseDate = app.datetime.fromJSDate(baseDate).minus({days: sectionTerm.toleranceDays}).toJSDate();
            }
        }

        return baseDate;
    }

    async function initItem(item, itemExtra = {}) {
        if (!_.isString(item._id)) item._id = Random.id(8);
        item.currencyId = order.currencyId;
        item.currencyRate = order.currencyRate;
        item.isFixed = false;
        if (!_.isInteger(item.no)) item.no = 1;
        if (!_.isNumber(item.baseTotal)) item.baseTotal = 0;
        if (!_.isNumber(item.total)) item.total = 0;
        item.branchId = order.branchId;
        item.reference = order.code;
        item.description = app.translate('Receipts');
        item.issueDate = order.orderDate;
        item.recordDate = order.recordDate;
        item.countryId = company.address.countryId;
        if (!item.scope) item.scope = '1';

        if (item.type === 'pos') {
            const journal = await app.collection('accounting.journals').findOne({
                _id: paymentMethod.journalId,
                $disableBranchCheck: true
            });
            const pos = await app.collection('accounting.pos').findOne({
                journalId: journal._id,
                $disableBranchCheck: true
            });
            const bankAccount = await app.collection('accounting.bank-accounts').findOne({
                _id: pos.bankAccountId,
                $disableBranchCheck: true
            });
            const paymentJournal = await app.collection('accounting.journals').findOne({
                _id: bankAccount.journalId,
                $disableBranchCheck: true,
                $select: ['_id']
            });
            const baseDate = findBaseDate();
            const now = app.datetime.fromJSDate(baseDate).startOf('day');

            const extra = {};
            extra.installmentCount = item.installmentCount;
            extra.plusInstallmentCount = item.plusInstallmentCount || 0;
            extra.installmentAmount = 0;
            extra.dueDifference = 0;
            extra.total = 0;
            extra.documentNo = itemExtra.documentNo || null;
            extra.partnerCreditCardId = itemExtra.partnerCreditCardId || null;
            // extra.cardBrand = itemExtra.cardBrand || null;
            // extra.cardHolder = itemExtra.cardHolder || null;
            // extra.cardNumber = itemExtra.cardNumber || null;
            // extra.expireMonth = itemExtra.expireMonth || null;
            // extra.expireYear = itemExtra.expireYear || null;

            // if (
            //     !!pos &&
            //     pos.isVirtual &&
            //     Array.isArray(pos.commissions) &&
            //     pos.commissions.length > 0 &&
            //     !pos.commissions.some(c => !c.cardBrand)
            // ) {
            //     if (!model.selectedCardBrand) {
            //         this.model.selectedCardBrand = model.selectedCardBrand = this.pos.commissions[0].cardBrand;
            //     }

            //     this.pos.commissions = this.pos.commissions.filter(c => c.cardBrand === model.selectedCardBrand);
            // }

            const installment = pos.commissions.find(c => c.installment === extra.installmentCount);
            let installmentCount = extra.installmentCount;
            let installmentAmount = 0;
            let dueDifference = 0;
            let total = 0;

            if (pos.commissionType !== 'within' && !!installment) {
                dueDifference = round(
                    (order.grandTotal * (installment.commission || 0)) / 100 +
                        (order.grandTotal * (installment.serviceCommission || 0)) / 100,
                    currencyPrecision
                );
            }

            total = order.grandTotal + dueDifference;

            if (
                !!installment &&
                _.isNumber(installment.interestRate) &&
                installment.interestRate > 0 &&
                pos.commissionType !== 'within'
            ) {
                const dueDate = app.datetime
                    .fromJSDate(order.issueDate)
                    .startOf('day')
                    .plus({months: extra.installmentCount});
                let dueDateDaysDifference = dueDate.diff(now, 'days').toObject().days;

                if (pos.dueType === 'average-due-difference') {
                    let newDueDateDaysDifference = dueDate.plus({months: 1}).diff(now, 'days').toObject().days;

                    dueDateDaysDifference = round(newDueDateDaysDifference / 2);
                }

                const diff = round(
                    ((total * installment.interestRate) / 3000) * dueDateDaysDifference,
                    currencyPrecision
                );

                dueDifference += diff;
                total += diff;
            }

            installmentAmount = round(total / installmentCount, currencyPrecision);

            extra.installmentAmount = installmentAmount;
            extra.dueDifference = dueDifference;
            extra.total = total;

            item.posId = pos._id;
            item.branchId = journal.branchId;
            item.journalId = pos.journalId;
            item.paymentAccountId = paymentJournal._id;
            item.currencyId = pos.currencyId;
            item.baseTotal = item.baseTotal || extra.baseTotal || 0;
            item.total = extra.total;
            item.installmentCount = extra.installmentCount;
            item.plusInstallmentCount = extra.plusInstallmentCount;
            item.installmentAmount = extra.installmentAmount;
            item.dueDifference = extra.dueDifference;
            item.documentNo = extra.documentNo;
            item.partnerCreditCardId = extra.partnerCreditCardId;
            // item.cardBrand = extra.cardBrand;
            // item.cardHolder = extra.cardHolder;
            // item.cardNumber = extra.cardNumber;
            // item.expireMonth = extra.expireMonth;
            // item.expireYear = extra.expireYear;
            // item.cvv = extra.cvv;
            item.journalId = journal._id;
            item.journalName = journal.name;

            // Get due date.
            if (pos.paymentOnSpecificDate) {
                const cutoffDate = pos.cutoffDate;

                if (now.day > cutoffDate) {
                    item.dueDate = now.plus({months: 1}).set({day: cutoffDate}).toJSDate();
                } else {
                    item.dueDate = now.set({day: cutoffDate}).toJSDate();
                }

                item.dueDate = app.datetime
                    .fromJSDate(item.dueDate)
                    .plus({months: extra.installmentCount - 1})
                    .toJSDate();
            } else {
                if (pos.posRefund === 'lump-sum-payment') {
                    const lumpSumPayment = pos.lumpSumPayments.find(c => c.installment === extra.installmentCount);

                    if (!!lumpSumPayment) {
                        item.dueDate = now.plus({days: lumpSumPayment.refund || 0}).toJSDate();
                    }
                } else {
                    const installmentPayment = pos.installmentPayments.find(
                        c => c.installment === extra.installmentCount
                    );

                    if (!!installmentPayment) {
                        item.dueDate = now.plus({days: installmentPayment.refund || 0}).toJSDate();
                    }
                }
            }

            const dueDate = app.datetime.fromJSDate(item.dueDate);
            let dueDayDifference = dueDate.diff(now, 'days').toObject().days;
            if (pos.dueType === 'average-due-difference') {
                let newDueDaysDifference = dueDate.plus({months: 1}).diff(now, 'days').toObject().days;

                dueDayDifference = round(newDueDaysDifference / 2);
            }
            item.dueDayDifference = dueDayDifference;

            if (_.isDate(item.dueDate)) {
                item.dueDate = app.datetime.fromJSDate(item.dueDate).startOf('day').toJSDate();
            }

            item.dueDifference = item.total - item.baseTotal;
            item.posTotal = item.total;
        } else {
            const journal = await app.collection('accounting.journals').findOne({
                _id: paymentMethod.journalId,
                $select: ['name']
            });

            item.journalId = journal._id;
            item.journalName = journal.name;
            item.documentNo = itemExtra.documentNo || null;
        }

        return item;
    }

    let installmentCount = parseInt(orderPayload.installmentCount);
    if (!_.isNumber(installmentCount) || _.isNaN(installmentCount) || installmentCount === 0) {
        installmentCount = 1;
    }
    let installmentAmount = order.grandTotal / installmentCount;
    let items = [];

    let paymentType = 'cash';
    if (paymentMethod.paymentType === 'credit-card') {
        paymentType = 'pos';
    } else if (paymentMethod.paymentType === 'money-order' || paymentMethod.paymentType === 'cash-on-delivery') {
        paymentType = 'moneyTransfer';
    }

    if (paymentType === 'pos') {
        const installment = {};

        installment.type = 'pos';
        installment.index = items.length + 1;
        installment.isFixed = true;
        installment.baseTotal = order.grandTotal;
        installment.installmentCount = installmentCount;
        installment.installmentAmount = installmentAmount;

        items.push(await initItem(installment, orderPayload || {}));
    } else {
        for (let i = 1; i <= installmentCount; i++) {
            const sectionTerm = paymentTerm[paymentType];
            const baseDate = findBaseDate(paymentType, order.orderDate);
            const installmentPeriodMonths = sectionTerm.installmentPeriodMonths || 1;
            const installmentPeriodDays = sectionTerm.installmentPeriodDays || 0;
            const installment = {};

            let dueDate = app.datetime.fromJSDate(baseDate);
            dueDate = dueDate.plus({months: installmentPeriodMonths * (i - 1)});
            dueDate = dueDate.plus({days: installmentPeriodDays * (i - 1)});

            installment.type = paymentType;
            installment.index = i;
            installment.dueDate = dueDate.toJSDate();
            installment.baseTotal = round(installmentAmount, currencyPrecision);
            installment.total = round(installmentAmount, currencyPrecision);

            items.push(await initItem(installment, orderPayload));
        }
    }

    const typeNoIncrementMap = {};
    items = items.map(item => {
        if (_.isNumber(typeNoIncrementMap[item.type])) {
            item.no = typeNoIncrementMap[item.type] + 1;
            typeNoIncrementMap[item.type]++;
        } else {
            item.no = 1;
            typeNoIncrementMap[item.type] = 1;
        }

        return item;
    });
    if (items.length > 0) {
        const total = round(_.sumBy(items, 'total'), currencyPrecision);
        const totalDiff = order.grandTotal - total;

        items[items.length - 1].total += totalDiff;
    }
    if (items.length > 0) {
        const total = round(_.sumBy(items, 'baseTotal'), currencyPrecision);
        const totalDiff = order.grandTotal - total;

        items[items.length - 1].baseTotal += totalDiff;
    }

    if (items.length > 0) {
        const baseDate = findBaseDate();
        const grouped = _.groupBy(items, 'type');
        const amount = round(_.sumBy(items, 'total'), currencyPrecision);

        for (const section of Object.keys(grouped)) {
            const items = grouped[section];

            if (section === 'pos') {
                const r = {
                    baseTotal: 0,
                    total: 0,
                    dueDate: now.toJSDate(),
                    dueDayDifference: 0,
                    avgDueDate: now.toJSDate(),
                    avgDueDayDifference: 0
                };

                const dueDateSortedItems = _.orderBy(items, ['dueDate'], ['desc']);
                if (dueDateSortedItems.length > 0) {
                    r.dueDate = dueDateSortedItems[0].dueDate;
                }
                r.dueDate = app.datetime.fromJSDate(r.dueDate).startOf('day').toJSDate();

                const dueDateStart = app.datetime.fromJSDate(baseDate);
                const dueDateEnd = app.datetime.fromJSDate(r.dueDate);
                r.dueDayDifference = Math.ceil(dueDateEnd.diff(dueDateStart, 'days').toObject().days);
                r.baseTotal = _.sumBy(items, item => round(item.baseTotal * item.currencyRate, currencyPrecision));
                r.difference = _.sumBy(items, item => round(item.dueDifference * item.currencyRate, currencyPrecision));
                r.total = _.sumBy(items, item => round(item.posTotal * item.currencyRate, currencyPrecision));
                r.changing = r.baseTotal > 0 ? (r.difference / r.baseTotal) * 100 : 0;

                // Avg. due date and Avg. due day difference.
                if (r.baseTotal > 0) {
                    let adat = 0;

                    items.forEach(item => {
                        adat += item.dueDayDifference * item.posTotal;
                    });

                    r.avgDueDayDifference = round(adat / r.total);
                    r.avgDueDate = app.datetime.fromJSDate(baseDate).plus({days: r.avgDueDayDifference}).toJSDate();
                }

                sectionReport['pos'] = r;
            } else {
                const vm = {
                    $app: app,
                    $datetime: app.datetime,
                    $setting: app.setting,
                    model: {
                        discount: 0
                    }
                };
                sectionReport[section] = calculateReport(vm, items, findBaseDate(section), paymentTerm[section]);
            }

            _.each(sectionReport, r => {
                report.baseTotal += round(r.baseTotal || 0, currencyPrecision);
                report.difference += round(r.difference || 0, currencyPrecision);
                report.total += round(r.total || 0, currencyPrecision);

                if (r.dueDate.getTime() > report.dueDate.getTime()) {
                    report.dueDate = r.dueDate;

                    const dueDateStart = app.datetime.fromJSDate(baseDate).startOf('day');
                    const dueDateEnd = app.datetime.fromJSDate(report.dueDate).startOf('day');
                    report.dueDayDifference = round(dueDateEnd.diff(dueDateStart, 'days').toObject().days);
                }
            });

            if (report.baseTotal > 0) {
                let adat = 0;

                _.each(sectionReport, prop => {
                    if (_.isNumber(prop.avgDueDayDifference)) {
                        adat += prop.baseTotal * prop.avgDueDayDifference;
                    }
                });

                report.avgDueDayDifference = round(adat / report.baseTotal);
                report.avgDueDate = app.datetime
                    .fromJSDate(baseDate)
                    .plus({days: report.avgDueDayDifference})
                    .toJSDate();
            }

            report.difference = round(report.difference, currencyPrecision);
            report.differencePercentage = _.isNumber(report.difference)
                ? round((report.difference / report.total) * 100, percentagePrecision)
                : 0;
            report.total = round(report.total, currencyPrecision);
            report.openAmount = round(amount - report.baseTotal, currencyPrecision);
            if (amount > 0) {
                report.changing = round((report.difference / amount) * 100, percentagePrecision);
            }
        }
    }

    const data = {};
    data.report = report;
    data.sectionReport = sectionReport;
    data.baseDate = findBaseDate();
    data.items = [];

    for (const row of items || []) {
        const item = {};

        item.documentType = row.type;
        item.id = row._id ? row._id : Random.id(16);
        item.no = row.no;
        item.documentNo = row.documentNo;
        item.currencyId = row.currencyId;
        item.currencyRate = row.currencyRate;
        item.dueDate = row.dueDate;
        item.total = row.total;
        item.amount = row.total;
        item.baseTotal = row.baseTotal;
        item.rounding = row.rounding || 0;
        item.journalId = row.journalId;
        item.isFixed = row.isFixed;
        item.branchId = row.branchId;
        item.scope = _.isString(row.scope) && row.scope ? row.scope : '1';
        item.guaranteeId = row.guaranteeId;
        item.description = row.description;
        item.reference = row.reference;
        item.recordDate = row.recordDate;
        item.issueDate = row.issueDate;
        item.issuedBy = row.issuedBy;
        item.status = row.status ? row.status : 'waiting';

        if (!item.transactionType) {
            item.transactionType = 'other';
        }

        if (row.type === 'pos') {
            item.posId = row.posId;
            item.paymentAccountId = row.paymentAccountId;
            item.amount = row.total - (row.dueDifference || 0);
            item.installmentCount = row.installmentCount;
            item.plusInstallmentCount = row.plusInstallmentCount;
            item.installmentAmount = row.installmentAmount;
            item.dueDifference = row.dueDifference;
            item.partnerCreditCardId = row.partnerCreditCardId;
            // item.cardBrand = row.cardBrand;
            // item.cardHolder = row.cardHolder;
            // item.cardNumber = row.cardNumber;
            // item.expireMonth = row.expireMonth;
            // item.expireYear = row.expireYear;
            // item.cvv = row.cvv;
            item.dueDayDifference = row.dueDayDifference;
            item.dueDifference = row.dueDifference;
            item.posTotal = row.posTotal;
        }

        data.items.push(item);
    }
    const journals = await app.collection('accounting.journals').find({
        _id: {$in: data.items.filter(i => !!i.journalId).map(i => i.journalId)},
        $select: ['name'],
        $disableBranchCheck: true,
        $disableActiveCheck: true,
        $disableSoftDelete: true
    });
    const branches = await app.collection('kernel.branches').find({
        _id: {$in: data.items.filter(i => !!i.branchId).map(i => i.branchId)},
        $select: ['name'],
        $disableActiveCheck: true,
        $disableSoftDelete: true
    });
    data.items = data.items.map(item => {
        if (item.journalId) {
            item.journalName = journals.find(j => j._id === item.journalId).name;
        }
        if (item.branchId) {
            item.branchName = branches.find(j => j._id === item.branchId).name;
        }

        return item;
    });

    // Find scope rate.
    let scope1Total = 0;
    let scope2Total = 0;
    let scopeTotal = 0;
    for (const item of data.items) {
        if (item.scope === '1') {
            scope1Total += item.total;
        } else {
            scope2Total += item.total;
        }
        scopeTotal += item.total;
    }
    data.scopeRate = scope1Total / scopeTotal;

    return {paymentTerm, paymentPlan: data};
}

export async function getCustomer({app, company, store, cart, now, user}) {
    const result = {
        customer: null,
        contact: null,
        invoiceResponsibleId: null,
        invoiceAddressId: null,
        invoiceAddress: null,
        deliveryReceiverId: null,
        deliveryAddressId: null,
        deliveryAddress: null,
        isNewCustomer: false
    };

    if (!!cart.customerId) {
        const customer = await app.collection('kernel.partners').findOne({
            _id: cart.customerId,
            $disableActiveCheck: true,
            $disableSoftDelete: true
        });
        const billingContact = await app.collection('kernel.contacts').findOne({
            type: 'invoice-address',
            _id: cart.billingAddressId,
            $disableActiveCheck: true,
            $disableSoftDelete: true
        });
        const deliveryContact = await app.collection('kernel.contacts').findOne({
            type: 'delivery-address',
            _id: cart.deliveryAddressId,
            $disableActiveCheck: true,
            $disableSoftDelete: true
        });

        if (!customer.identity) {
            customer.identity = '***********';
        }
        if (!customer.invoiceScenario) {
            customer.invoiceScenario = 'e-archive-invoice';
        }

        // Assign values.
        result.isNewCustomer = false;
        result.customer = customer;
        result.contact = billingContact;
        result.invoiceResponsibleId = billingContact.relevantContactId;
        result.invoiceAddressId = billingContact._id;
        result.invoiceAddress = billingContact.address;
        result.deliveryReceiverId = deliveryContact.relevantContactId;
        result.deliveryAddressId = deliveryContact._id;
        result.deliveryAddress = deliveryContact.address;

        // Fix.
        const cartDeliveryAddress = await normalizeAddress(app, cart.deliveryAddress);
        if (!!deliveryContact && deliveryContact.address.address !== cartDeliveryAddress.address) {
            let newDeliveryContact = {};
            newDeliveryContact.type = 'delivery-address';
            newDeliveryContact.name = app.translate('Delivery Address');
            newDeliveryContact.partnerId = customer._id;
            newDeliveryContact.partnerType = 'customer';
            newDeliveryContact.relevantContactId = deliveryContact.relevantContactId;
            newDeliveryContact.email = customer.email;
            if (!!customer.phone) {
                newDeliveryContact.phone = customer.phone;
                newDeliveryContact.phoneCountryCode = customer.phoneCountryCode;
                newDeliveryContact.phoneNumbers = customer.phoneNumbers;
            }
            newDeliveryContact.address = cartDeliveryAddress;
            newDeliveryContact = await app.collection('kernel.contacts').create(newDeliveryContact, {user});

            cart.deliveryAddressId = newDeliveryContact._id;
            await app.collection('store.cart').patch(
                {_id: cart._id},
                {
                    deliveryAddressId: newDeliveryContact._id
                }
            );
            result.deliveryAddressId = newDeliveryContact._id;
            result.deliveryAddress = newDeliveryContact.address;
        }
    } else {
        const numbering = await app.collection('kernel.numbering').findOne(
            {
                code: 'partnerCustomerNumbering',
                $select: ['_id']
            },
            {
                disableInUseCheck: true,
                disableActiveCheck: true,
                disableSoftDelete: true
            }
        );
        let customer = {};

        // Addresses.
        const billingAddress = await normalizeAddress(app, cart.billingAddress);
        const deliveryAddress = await normalizeAddress(app, cart.deliveryAddress);

        // Phone numbers.
        const phoneNumbers = [];
        let phone = null;
        let phoneCountryCode = null;
        if (!!cart.phoneCountryCode && !!cart.phoneCode && !!cart.phoneNumber) {
            phone = trim(cart.phoneNumber || '');
            phoneCountryCode = trim(cart.phoneCountryCode || '');

            phoneNumbers.push({
                type: 'work',
                countryCode: phoneCountryCode,
                phoneCode: cart.phoneCode,
                number: phone
            });
        }

        // General.
        customer.type = 'customer';
        customer.code = await app.rpc('kernel.common.request-number', {
            numberingId: numbering._id,
            save: true
        });
        customer.name = trim(`${cart.firstName || ''} ${cart.lastName || ''}`);
        customer.firstName = trim(cart.firstName || '');
        customer.lastName = trim(cart.lastName || '');
        customer.email = trim(cart.email);
        customer.groupId = store.customerGroupId;
        customer.countryId = billingAddress.countryId;
        customer.timezone = app.config('app.timezone');
        customer.currencyId = store.currencyId;
        customer.languageId = store.languageId;
        customer.branchIds = [store.branchId];
        customer.identity = '***********';
        customer.invoiceScenario = 'e-archive-invoice';
        customer.address = billingAddress;
        customer.accountingAccountId = app.defaultAccountingAccount('domesticAccountsReceivableAccount', 'sale');
        customer.isSubscribedToNewsletter = !!cart.isSubscribedToNewsletter;

        // Phone number.
        if (!!phone) {
            customer.phone = phone;
            customer.phoneCountryCode = phoneCountryCode;
            customer.phoneNumbers = phoneNumbers;
        }

        // E-Invoice type.
        const eInvoiceTypes = await app.collection('eops.e-invoice-types').find({
            scenarios: customer.invoiceScenario,
            $select: ['_id']
        });
        if (eInvoiceTypes.length > 0) {
            customer.eInvoiceTypeId = eInvoiceTypes[0]._id;
        }

        // Default limit definition.
        const pld = await app.collection('finance.partner-limit-definitions').findOne({
            partnerType: 'customer',
            $or: [{partnerGroupId: {$exists: false}}, {partnerGroupId: {$eq: null}}, {partnerGroupId: customer.groupId}]
        });
        if (_.isPlainObject(pld)) {
            customer.enableLimitChecks = pld.enableLimitChecks;
            customer.limitControlDocument = pld.limitControlDocument;
            customer.limitOrderControl = pld.limitOrderControl;
            customer.limitInvoiceControl = pld.limitInvoiceControl;
            customer.openAccountLimit = pld.openAccountLimit;
            customer.totalLimit = pld.openAccountLimit;
        }

        if ((await app.collection('kernel.partners').count({code: customer.code})) > 0) {
            customer.code = microtime.now();
        }

        // Create customer.
        customer = await app.collection('kernel.partners').create(customer, {user});

        // Contact.
        let contact = {};
        contact.type = 'contact';
        contact.name = trim(`${cart.firstName || ''} ${cart.lastName || ''}`);
        contact.partnerId = customer._id;
        contact.partnerType = 'customer';
        contact.email = customer.email;
        if (!!phone) {
            contact.phone = phone;
            contact.phoneCountryCode = phoneCountryCode;
            contact.phoneNumbers = phoneNumbers;
        }
        contact.address = billingAddress;
        contact = await app.collection('kernel.contacts').create(contact, {user});

        // Billing address.
        let billingAddressContact = {};
        billingAddressContact.type = 'invoice-address';
        billingAddressContact.name = app.translate('Invoice Address');
        billingAddressContact.partnerId = customer._id;
        billingAddressContact.relevantContactId = contact._id;
        billingAddressContact.partnerType = 'customer';
        billingAddressContact.email = customer.email;
        if (!!phone) {
            billingAddressContact.phone = phone;
            billingAddressContact.phoneCountryCode = phoneCountryCode;
            billingAddressContact.phoneNumbers = phoneNumbers;
        }
        billingAddressContact.address = billingAddress;
        if (cart.invoiceType === 'corporate') {
            billingAddressContact.invoiceType = 'corporate';
            billingAddressContact.companyName = trim(cart.companyName || '');
            billingAddressContact.tin = trim(cart.taxIdentificationNumber || '');
            billingAddressContact.taxDepartment = trim(cart.taxOffice || '');
        } else {
            billingAddressContact.invoiceType = 'individual';
            billingAddressContact.identity = trim(cart.identityNumber || '');
        }
        billingAddressContact = await app.collection('kernel.contacts').create(billingAddressContact, {user});

        // Delivery address.
        let deliveryAddressContact = {};
        deliveryAddressContact.type = 'delivery-address';
        deliveryAddressContact.name = app.translate('Delivery Address');
        deliveryAddressContact.partnerId = customer._id;
        deliveryAddressContact.relevantContactId = contact._id;
        deliveryAddressContact.partnerType = 'customer';
        deliveryAddressContact.email = customer.email;
        if (!!phone) {
            deliveryAddressContact.phone = phone;
            deliveryAddressContact.phoneCountryCode = phoneCountryCode;
            deliveryAddressContact.phoneNumbers = phoneNumbers;
        }
        deliveryAddressContact.address = deliveryAddress;
        deliveryAddressContact = await app.collection('kernel.contacts').create(deliveryAddressContact, {user});

        // Assign values.
        result.isNewCustomer = true;
        result.customer = customer;
        result.contact = contact;
        result.invoiceResponsibleId = contact._id;
        result.invoiceAddressId = billingAddressContact._id;
        result.invoiceAddress = billingAddressContact.address;
        result.deliveryReceiverId = contact._id;
        result.deliveryAddressId = deliveryAddressContact._id;
        result.deliveryAddress = deliveryAddressContact.address;
    }

    return result;
}

export async function normalizeAddress(app, address) {
    const country = await app.collection('kernel.countries').findOne({
        _id: address.countryId,
        $select: ['name', 'addressFormat'],
        $disableSoftDelete: true,
        $disableActiveCheck: true
    });
    const normalized = {};

    let addressFormat = country.addressFormat;
    if (!addressFormat) {
        addressFormat = `{{street}}\n{{subDistrict}} {{district}} {{city}} {{postalCode}}\n{{country}}`;
    }

    normalized.countryId = trim(address.countryId);
    normalized.city = trim(address.city || '');
    normalized.district = trim(address.district || '');
    normalized.subDistrict = trim(address.subDistrict || '');
    normalized.state = trim(address.state || '');
    normalized.street = trim(`${address.street || ''} ${address.street2 || ''}`);
    normalized.postalCode = trim(address.postalCode || '');
    normalized.apartmentNumber = '';
    normalized.doorNumber = '';
    normalized.address = '';

    let formatted = '';
    formatted = template(addressFormat, {
        subDistrict: normalized.subDistrict,
        street: normalized.street,
        apartmentNumber: normalized.apartmentNumber,
        doorNumber: normalized.doorNumber,
        postalCode: normalized.postalCode,
        district: normalized.district,
        city: normalized.city,
        state: normalized.state,
        country: country.name
    });
    formatted = formatted.trim();
    formatted = formatted.replace(' /', ' ');
    formatted = formatted.replace('/ ', ' ');
    formatted = formatted.replace('/,', ',');
    formatted = formatted.replace(',/', ',');
    formatted = formatted.replace('No: ,', ',');
    formatted = formatted
        .split(' ')
        .filter(part => !_.isEmpty(part.trim()) && part.trim() !== '/')
        .join(' ');
    formatted = formatted
        .split(',')
        .filter(part => !_.isEmpty(part.trim()) && part.trim() !== '/')
        .join(',');
    formatted = formatted.trim();
    if (formatted[0] === '/') {
        formatted = formatted.slice(1);
    }
    normalized.address = formatted;

    return normalized;
}

export async function checkAndSendB2bMail(app, company, store, cart, order, customer) {
    try {
        console.log('=== B2B Mail Check ===');
        console.log('Store useB2BPolicies:', store.useB2BPolicies);
        console.log('Store sendB2bMail:', store.sendB2bMail);
        console.log('Customer groupId:', customer.groupId);
        console.log('Store allowedPartnerGroupIds:', store.allowedPartnerGroupIds);

        // Check if B2B mail is enabled for this store
        if (!store.useB2BPolicies || !store.sendB2bMail) {
            console.log('B2B policies or B2B mail not enabled - skipping');
            return; // B2B policies or B2B mail not enabled
        }

        // Check if this customer/order matches any B2B policy
        let isB2bOrder = false;

        // Check if customer is in allowed partner groups for B2B
        if (Array.isArray(store.allowedPartnerGroupIds) &&
            store.allowedPartnerGroupIds.length > 0 &&
            store.allowedPartnerGroupIds.includes(customer.groupId)) {
            isB2bOrder = true;
            console.log('Customer is in B2B group - sending B2B mail');
        } else {
            console.log('Customer is NOT in B2B group - skipping B2B mail');
        }

        if (isB2bOrder) {
            await sendB2bCompanyMail(app, company, store, cart, order, customer);
            console.log('B2B company mail sent successfully');
        }
    } catch (error) {
        console.error('B2B mail check error:', error.message);
    }
}

export async function sendB2bCompanyMail(app, company, store, cart, order, customer) {
    try {
        const t = text => app.translate(text);
        const siteUrl = trim(store.website, '/');
        const deliveryAddress = await normalizeAddress(app, cart.deliveryAddress);
        const billingAddress = await normalizeAddress(app, cart.billingAddress);

        const template = `
<mj-wrapper full-width
            background-color="#e0f2fe"
            padding="24px"
            css-class="content-wrapper">
    <mj-section padding-bottom="48px" padding-top="32px">
        <mj-column>
            <mj-image align="center" src="${app.absoluteUrl('static/images/mail/order-received.png')}" width="150px"/>
        </mj-column>
    </mj-section>

    <mj-section>
        <mj-column>
            <mj-text font-size="21px"
                     color="#0369a1"
                     font-weight="600" padding="0" padding-bottom="12px">${t('New B2B Order Received')}</mj-text>
            <mj-text>${t('A new B2B order has been placed on your store.')}</mj-text>
        </mj-column>
    </mj-section>

    <mj-section padding-top="16px">
        <mj-column padding-bottom="12px">
            <mj-text font-size="13px" font-weight="600" padding="0">${t('ORDER NUMBER')}</mj-text>
            <mj-text font-size="13px" padding="0">
                <a href="${siteUrl}/account/my-orders/${order._id}">${order.code}</a>
            </mj-text>
        </mj-column>

        <mj-column>
            <mj-text font-size="13px" font-weight="600" padding="0">${t('TOTAL AMOUNT')}</mj-text>
            <mj-text font-size="13px" padding="0">${app.format(cart.grandTotal || 0, 'currency')}</mj-text>
        </mj-column>
    </mj-section>

    <mj-section padding-top="12px">
        <mj-column>
            <mj-text font-size="13px" font-weight="600" padding="0">${t('CUSTOMER')}</mj-text>
            <mj-text font-size="13px" padding="0">${customer.name}</mj-text>
        </mj-column>
    </mj-section>

    <mj-section padding-top="12px">
        <mj-column>
            <mj-text font-size="13px" font-weight="600" padding="0">${t('DELIVERY ADDRESS')}</mj-text>
            <mj-text font-size="13px" padding="0">${deliveryAddress.address}</mj-text>
        </mj-column>
    </mj-section>
</mj-wrapper>

<mj-section padding-top="48px" padding-bottom="16px">
    <mj-column>
        <mj-text padding="0" font-size="18px" font-weight="600">${t('Order Summary')}</mj-text>
    </mj-column>
</mj-section>
<mj-wrapper background-color="white"
            padding-left="24px"
            padding-right="24px"
            padding-bottom="12px"
            padding-top="24px"
            css-class="content-wrapper-with-border">
${cart.items
    .map(item => {
        return `
<mj-section>
    <mj-column width="15%">
        <mj-image padding="0" padding-right="32px" padding-bottom="12px" src="${item.productImage}.png"/>
    </mj-column>

    <mj-column width="85%">
        <mj-text font-size="15px" padding="0">
            <a href="${!!item.productLink ? `${siteUrl}${item.productLink}` : `${siteUrl}/${item.productSlug}`}">
                ${item.productName}
            </a>
        </mj-text>
        <mj-text padding="0" padding-top="8px" font-size="14px" font-weight="500">
            ${item.quantity} x ${app.format(
            (typeof item.discountedPrice === 'number' && item.discountedPrice > 0
                ? item.discountedPrice
                : item.price) || 0,
            'currency'
        )}
        </mj-text>
    </mj-column>
</mj-section>

<mj-section>
    <mj-column>
        <mj-divider padding-top="16px" padding-bottom="16px"/>
    </mj-column>
</mj-section>
`;
    })
    .join('\n')}

    <mj-section>
       <mj-group>
           <mj-column>
               <mj-text font-size="14px" padding="0" line-height="21px">
                   ${t('Products Total')} (${cart.productCount})
               </mj-text>
           </mj-column>

           <mj-column>
               <mj-text align="right" font-size="14px" padding="0" line-height="21px">
                   ${app.format(cart.subTotal || 0, 'currency')}
               </mj-text>
           </mj-column>
       </mj-group>
    </mj-section>
    <mj-section padding-top="4px">
        <mj-group>
            <mj-column>
                <mj-text font-size="14px" padding="0" line-height="21px">
                    ${t('Tax total')}
                </mj-text>
            </mj-column>

            <mj-column>
                <mj-text align="right" font-size="14px" padding="0" line-height="21px">
                      ${app.format(cart.taxTotal || 0, 'currency')}
                </mj-text>
            </mj-column>
        </mj-group>
    </mj-section>
    <mj-section padding-top="4px">
        <mj-group>
            <mj-column>
                <mj-text font-size="14px" padding="0" line-height="21px">
                    ${t('Delivery amount')}
                </mj-text>
            </mj-column>

            <mj-column>
                <mj-text align="right" font-size="14px" padding="0" line-height="21px">
                      ${app.format(cart.deliveryTotal || 0, 'currency')}
                </mj-text>
            </mj-column>
        </mj-group>
    </mj-section>
    <mj-section padding-top="8px">
       <mj-group>
           <mj-column>
               <mj-text font-size="16px" padding="0" font-weight="600" line-height="21px">
                    ${t('Grand total')}
               </mj-text>
           </mj-column>

           <mj-column>
               <mj-text align="right" font-weight="600" font-size="16px" padding="0" line-height="21px">
                    ${app.format(cart.grandTotal || 0, 'currency')}
               </mj-text>
           </mj-column>
       </mj-group>
    </mj-section>

    <mj-section padding-top="16px">
        <mj-column>
            <mj-divider padding-top="16px" padding-bottom="16px"/>
        </mj-column>
    </mj-section>
    <mj-section padding-top="0">
        <mj-column padding-bottom="12px">
            <mj-text font-size="14px" font-weight="600" padding="0"  padding-bottom="8px">${t(
                'Customer Information'
            )}</mj-text>
            <mj-text font-size="13px" padding="0" padding-bottom="4px" line-height="21px">
                ${customer.name}
            </mj-text>
            <mj-text font-size="13px" padding="0" line-height="21px">${customer.email || ''}</mj-text>
            <mj-text font-size="13px" padding="0" line-height="21px">${customer.phone || ''}</mj-text>
        </mj-column>

        <mj-column padding-bottom="12px">

        </mj-column>

        <mj-column padding-bottom="12px">
            <mj-text font-size="14px" font-weight="600" padding="0"  padding-bottom="8px">${t(
                'Invoice Information'
            )}</mj-text>
            <mj-text font-size="13px" padding="0" padding-bottom="4px" line-height="21px">
                ${!!cart.companyName ? cart.companyName : `${cart.firstName} ${cart.lastName}`}
            </mj-text>
            <mj-text font-size="13px" padding="0" line-height="21px">${billingAddress.address}</mj-text>
        </mj-column>
    </mj-section>
</mj-wrapper>
    `;

        await app.mail({
            from: `${store.name} <${store.email || company.email}>`,
            to: company.email,
            subject: `${app.translate('New B2B Order')} - ${order.code}`,
            noContentWrapper: true,
            template
        });
    } catch (error) {
        console.error('B2B company mail error:', error.message);
    }
}
