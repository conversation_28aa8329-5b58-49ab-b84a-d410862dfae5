.bi-dashboard-studio {
    position: relative;
    display: flex;
    flex-flow: column nowrap;
    width: 100%;
    height: 100%;
    background-color: $light-25;
    overflow: hidden;

    // Hide toolbar and chart types during export
    &.is-exporting {
        .dashboard-studio-toolbar,
        .dashboard-studio-chart-types {
            display: none !important;
        }
    }
}

// Canvas export optimizations
.dashboard-studio-canvas {
    &.is-exporting {
        // Tüm içeriği görünür yap
        overflow: visible !important;
        height: auto !important;

        // Grid layout'u tam boyutta göster
        .vue-grid-layout {
            height: auto !important;
            min-height: 100% !important;
        }

        // Chart wrapper'ları tam boyutta göster
        .dashboard-studio-chart-wrapper {
            overflow: visible !important;
        }
    }
}

// Layout container export optimizations
.layout-container {
    &.is-exporting {
        // Tüm içeriği görünür yap
        overflow: visible !important;
        height: auto !important;
        min-height: auto !important;

        // Grid item'ları tam boyutta göster
        .vue-grid-item {
            overflow: visible !important;
        }

        // Chart wrapper'ları tam boyutta göster
        .dashboard-studio-chart-wrapper {
            overflow: visible !important;
            box-shadow: none !important;
        }
    }
}
