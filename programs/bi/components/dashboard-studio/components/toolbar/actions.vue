<template>
    <div class="studio-toolbar-actions">
        <el-dropdown
            @command="handleAction"
            trigger="click"
            placement="bottom-end"
            @visible-change="isVisible => (isActionsVisible = isVisible)"
        >
            <el-button :class="{'is-actions-visible': isActionsVisible}" size="small" icon="fal fa-ellipsis-h" plain />
            <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="download-as-pdf" icon="fal fa-file-pdf">
                    {{ $t('Download as pdf') }}
                </el-dropdown-item>
                <el-dropdown-item command="download-as-image" icon="fal fa-file-image">
                    {{ $t('Download as image') }}
                </el-dropdown-item>
                <el-dropdown-item command="dashboard-settings" icon="fal fa-cog" v-if="canEdit">
                    {{ $t('Dashboard settings') }}
                </el-dropdown-item>
                <el-dropdown-item
                    command="delete-dashboard"
                    icon="fal fa-trash-alt"
                    class="text-danger"
                    v-if="canRemove"
                >
                    {{ $t('Delete dashboard') }}
                </el-dropdown-item>
            </el-dropdown-menu>
        </el-dropdown>
    </div>
</template>

<script>
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import saveAs from 'framework/save-as';
import store from '../../store';

export default {
    data: () => ({
        isActionsVisible: false
    }),

    computed: {
        canEdit() {
            return this.$app.hasPermission({
                type: 'record',
                name: 'bi.dashboards',
                method: 'update'
            });
        },
        canRemove() {
            return this.$app.hasPermission({
                type: 'record',
                name: 'bi.dashboards',
                method: 'remove'
            });
        },
        dashboard() {
            return store.state.dashboard;
        }
    },

    methods: {
        async handleAction(action) {
            if (action === 'download-as-image') {
                await this.handleDownloadAsImage();
            } else if (action === 'download-as-pdf') {
                await this.handleDownloadAsPdf();
            } else if (action === 'dashboard-settings') {
                this.handleDashboardSettings();
            } else if (action === 'delete-dashboard') {
                this.handleDeleteDashboard();
            }
        },

        async handleDownloadAsImage() {
            this.$params('loading', true);

            try {
                const dashboardEl = document.querySelector('.bi-dashboard-studio');
                if (!dashboardEl) {
                    throw new Error('Dashboard element not found');
                }

                // Layout container'ı bul (grid layout olan alan)
                const layoutEl = dashboardEl.querySelector('.layout-container');
                const targetEl = layoutEl || dashboardEl;

                // Export sınıfını ekle
                dashboardEl.classList.add('is-exporting');
                targetEl.classList.add('is-exporting');

                // Kısa bir bekleme süresi ekle ki CSS değişiklikleri uygulanabilsin
                await new Promise(resolve => setTimeout(resolve, 100));

                const canvas = await html2canvas(targetEl, {
                    backgroundColor: '#ffffff',
                    scale: 1.5,
                    useCORS: true,
                    allowTaint: true,
                    scrollX: 0,
                    scrollY: 0,
                    width: targetEl.scrollWidth || targetEl.offsetWidth,
                    height: targetEl.scrollHeight || targetEl.offsetHeight
                });

                // Export sınıflarını kaldır
                dashboardEl.classList.remove('is-exporting');
                targetEl.classList.remove('is-exporting');

                canvas.toBlob(blob => {
                    const fileName = this.dashboard?.title || 'dashboard';
                    saveAs(blob, `${fileName}.png`);

                    this.$nextTick(() => {
                        this.$params('loading', false);
                    });
                });
            } catch (error) {
                this.$program.message('error', error.message);
                this.$params('loading', false);
            }
        },

        async handleDownloadAsPdf() {
            this.$params('loading', true);

            try {
                const dashboardEl = document.querySelector('.bi-dashboard-studio');
                if (!dashboardEl) {
                    throw new Error('Dashboard element not found');
                }

                // Layout container'ı bul (grid layout olan alan)
                const layoutEl = dashboardEl.querySelector('.layout-container');
                const targetEl = layoutEl || dashboardEl;

                // Export sınıfını ekle
                dashboardEl.classList.add('is-exporting');
                targetEl.classList.add('is-exporting');

                // Kısa bir bekleme süresi ekle ki CSS değişiklikleri uygulanabilsin
                await new Promise(resolve => setTimeout(resolve, 100));

                const canvas = await html2canvas(targetEl, {
                    backgroundColor: '#ffffff',
                    scale: 1.5,
                    useCORS: true,
                    allowTaint: true,
                    scrollX: 0,
                    scrollY: 0,
                    width: targetEl.scrollWidth || targetEl.offsetWidth,
                    height: targetEl.scrollHeight || targetEl.offsetHeight
                });

                // Export sınıflarını kaldır
                dashboardEl.classList.remove('is-exporting');
                targetEl.classList.remove('is-exporting');

                const imgData = canvas.toDataURL('image/png');

                // PDF boyutlarını mm cinsinden hesapla
                const imgWidth = canvas.width;
                const imgHeight = canvas.height;
                const ratio = imgHeight / imgWidth;

                // A4 boyutları (mm)
                const a4Width = 210;
                const a4Height = 297;

                let pdfWidth, pdfHeight;

                if (ratio > a4Height / a4Width) {
                    // Yükseklik sınırlayıcı
                    pdfHeight = a4Height;
                    pdfWidth = a4Height / ratio;
                } else {
                    // Genişlik sınırlayıcı
                    pdfWidth = a4Width;
                    pdfHeight = a4Width * ratio;
                }

                const pdf = new jsPDF({
                    orientation: pdfWidth > pdfHeight ? 'landscape' : 'portrait',
                    unit: 'mm',
                    format: 'a4'
                });

                // Resmi PDF'e ekle - ortalayarak
                const x = (a4Width - pdfWidth) / 2;
                const y = (a4Height - pdfHeight) / 2;

                pdf.addImage(imgData, 'PNG', x, y, pdfWidth, pdfHeight);

                const fileName = this.dashboard?.title || 'dashboard';
                pdf.save(`${fileName}.pdf`);

                this.$params('loading', false);
            } catch (error) {
                this.$program.message('error', error.message);
                this.$params('loading', false);
            }
        },

        handleDashboardSettings() {
            // TODO: Implement dashboard settings functionality
            this.$program.message('info', 'Dashboard settings functionality will be implemented');
        },

        handleDeleteDashboard() {
            this.$program.alert('confirm', this.$t('Are you sure you want to delete this dashboard?'), async confirmed => {
                if (!confirmed) return;

                try {
                    this.$params('loading', true);
                    await this.$collection('bi.dashboards').remove({_id: this.dashboard._id});

                    // Navigate back to dashboard list
                    this.$parent.$parent.$emit('canceled');

                    this.$params('loading', false);
                } catch (error) {
                    this.$program.message('error', error.message);
                    this.$params('loading', false);
                }
            });
        }
    }
};
</script>
